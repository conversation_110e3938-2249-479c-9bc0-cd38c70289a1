.DEFAULT_GOAL := help
.PHONY: changes coverage install-main-deps install-test-deps install-all-deps lint publish push test

coverage:  ## Run tests with coverage
	coverage erase
	PYTHONPATH=src coverage run --include=src/app/* -m pytest -ra -c pytest_test.ini
	coverage report -m

install-main-deps:
	poetry install --no-root --only main

install-test-deps:
	poetry install --no-root --with test

install-all-deps:
	poetry install --no-root --with test,dev

lint:  ## Lint and static-check
	flake8 src/app
	mypy

dist:
	python setup.py sdist bdist_wheel

upload:  ## Publish to PyPi
	python -m twine upload dist/* --repository-url=https://gitlabnv2.flyaps.com/api/v4/projects/4/packages/pypi

push:  ## Push code with tags
	git push && git push --tags

test:  ## Run tests
	PYTHONPATH=src python -m pytest -ra -c pytest_test.ini

tox:   ## Run tox
	python -m tox

checkout-master:
	git fetch && git checkout master && git pull

changes: checkout-master
	@echo Changes from `git describe --abbrev=0 --tags` tag:
	@git log --no-merges --format='- %s%b' `git describe --abbrev=0 --tags`...origin/master | tee -p changes.txt


keycloak:
	@if [ `docker ps -f name=keycloak -q --all` ]; then \
		docker start keycloak; \
	else \
		docker run \
			-d \
			-e KEYCLOAK_USER=admin \
			-e KEYCLOAK_PASSWORD=admin \
			-e JAVA_OPTS_APPEND=-Dkeycloak.profile.feature.scripts=enabled \
			-e EXTERNAL_AUTH_APP_URL="https://localhost:8000/signin?token=foo-bar" \
			-e EXTERNAL_AUTH_APP_SECRET=secret \
			-p 8080:8080 \
			--name keycloak \
			uk-london-1.ocir.io/lrpfi3ly7ayq/nv2/core/nv2-core-keycloak-docker:latest; \
	fi; \
	docker logs -n 300 -f keycloak;

start:
	python -m app.main
