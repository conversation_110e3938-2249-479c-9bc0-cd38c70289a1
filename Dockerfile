FROM uk-london-1.ocir.io/lrpfi3ly7ayq/nv2/images/fastapi:3.10-poetry

ARG PIP_INDEX_URL
WORKDIR /app

RUN addgroup --system authorization && adduser --system --ingroup authorization authorization

COPY pyproject.toml poetry.lock ./

ARG NEXUS_USERNAME
ARG NEXUS_PASSWORD
ENV POETRY_HTTP_BASIC_NEXUS_USERNAME=$NEXUS_USERNAME
ENV POETRY_HTTP_BASIC_NEXUS_PASSWORD=$NEXUS_PASSWORD


ENV POETRY_VIRTUALENVS_CREATE=false
# RUN poetry self update
RUN poetry install --no-root

COPY src .
COPY manage.sh .
RUN chmod +x manage.sh

RUN chown -R authorization:authorization /app
USER authorization

ENTRYPOINT ["./manage.sh"]
CMD ["start_service"]
