# Organizations API

## Local setup actions
1. Create a database
2. Create the `.env` file:
```shell
cd <path-to-project-directory>
cp example.env src/.env
vim src/.env
# fill in the required variables
```
3. Install [poetry](https://python-poetry.org/docs/#installation)
4. (Optional) configure poetry to use the [existing virtual environment](https://python-poetry.org/docs/managing-environments/)
5. Activate the virtual environment(poetry will create one if not configured in previous step):
```shell
poetry shell
```
6. Install dependencies
```shell
make install-all-deps
```
7. Install pre-commit
```shell
pre-commit install
```
8. Start application
```shell
cd src
python -m app.main
```

## Running locally with OTEL instrumentation
Before running application via `opentelementry-instrumentation` export next environment variables:

```shell
export OTEL_SERVICE_NAME="organizations-api"
export OTEL_PYTHON_LOG_CORRELATION=true

# will not export any data
export OTEL_LOGS_EXPORTER="none"
export OTEL_TRACES_EXPORTER="none"
export OTEL_METRICS_EXPORTER="none"
```
