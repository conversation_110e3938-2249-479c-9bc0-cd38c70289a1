stages:
  - build
  - test
  - trivy
  - secscan
  - push
  - release
  - deploy

variables:
  # set to 'true' if you want continous delivery to DEV
  AUTO_DEPLOY_MASTER: 'false'
  CLUSTER_ENVIRONMENT:
    value: spogdev
    description: Please enter spogdev or spogtest
  # set how long to wait for deployment operations to complete
  DEPLOYMENT_TIMEOUT: 360s
  # set to 'true' if you want back deploy to OPS button
  DEPLOY_OPS: 'true'
  # exclude lint job from pipeline when set to 'true'
  # TEST_SKIP_LINT: 'false'
  # exclude mypy job from pipeline when set to 'true'
  # TEST_SKIP_MYPY: 'false'
  # exclude pytest job from pipeline when set to 'true'
  # TEST_SKIP_PYTEST: 'false'
  # set to 'true' if tests require database
  TEST_REQUIRES_POSTGRES: 'true'
  # set to 'true' if tests require Keycloak (implies previous variable)
  TEST_REQUIRES_KEYCLOAK: 'true'
  # use below variables to overwrite command of the corresponding jobs with your own
  # also variable ${COMMAND} is available and will be evaluated to default command from shared pipeline
  # TEST_COMMAND_LINT:
  # TEST_COMMAND_MYPY:
  TEST_COMMAND_PYTEST: >
    python -m tests_pre_start;
    coverage erase;
    coverage run --include=src/app/* -m pytest -ra --maxfail=2 --junitxml=${CI_JOB_NAME}/junit.xml;
    coverage report -m;
    coverage xml -o ${CI_JOB_NAME}/coverage.xml
  TEST_COMMAND_REQUIREMENTS: make install-test-deps


include:
  - project: devops/build
    file: /pipelines/build-docker-image.yaml
  # For information on shared pipeline with tests pleas read the document:
  # https://nextgenclearing.atlassian.net/wiki/spaces/DEVOPS/pages/454262803/How+to+customize+included+CI+test+stage
  - project: devops/test
    file: /pipelines/Authorization-test.yaml
  - project: devops/test
    file: /pipelines/trivy-scan.yaml
  - project: devops/test
    file: /pipelines/sec-scan.yaml
  - project: devops/tools
    file: /pipelines/tag-release-docker-image.yaml
  - project: devops/tools
    file: /pipelines/create-gitlab-release.yaml
  - project: devops/deploy
    file: /pipelines/trigger-deployment.yaml

# securityscan:
#   stage: test
#   extends: .common-python-test
#   needs: ["requirements"]
#   variables:
#     COMMAND: bandit -r -f html -o bandit.html -x tests  .
#   script:
#     - docker run ${CONTAINER_OPTIONS} -u root --network ${NETWORK_NAME} ${TEST_IMAGE} ${RUN_CMD} "${COMMAND}"
#   allow_failure: true
#   artifacts:
#     name: authorization-security-scan
#     expire_in: 1 week
#     paths:
#       - bandit.html
#     when: always
#   interruptible: true
#   rules:
#     - !reference [.python-test-rules, rules]

integration_dev_tests:
   #needs:
   # - lint
  stage: test
  image:
    name: postman/newman:alpine
    entrypoint: [""]
  before_script:
    - newman --version
    - npm install -g newman-reporter-html
    - npm install -g newman-reporter-htmlextra
  script:
    - newman run tests/integration/dev_integration/AuthorizationService_DA.json -e tests/integration/dev_integration/Dev_Environment_DA.json  --reporters cli,htmlextra --timeout-request 2500 --reporter-htmlextra-export ./report-da-dev.html || true
    - newman run tests/integration/dev_integration/AuthorizationService_CA.json -e tests/integration/dev_integration/Dev_Environment_CA.json  --reporters cli,htmlextra --timeout-request 2500 --reporter-htmlextra-export ./report-ca-dev.html
  allow_failure: true
  artifacts:
    when: always
    paths:
      - ./report-da-dev.html
      - ./report-ca-dev.html
  rules:
    - if: '$CLUSTER_ENVIRONMENT == "spogdev" && $CI_DEFAULT_BRANCH == "master"'
      when: manual

integration_qa_tests:
   #needs:
   # - lint
  stage: test
  image:
    name: postman/newman:alpine
    entrypoint: [""]
  before_script:
    - newman --version
    - npm install -g newman-reporter-html
    - npm install -g newman-reporter-htmlextra
  script:
    - newman run tests/integration/qa_integration/AuthorizationService_DA.json -e tests/integration/qa_integration/QA_Environment_DA.json  --reporters cli,htmlextra --timeout-request 2500 --reporter-htmlextra-export ./report-da-qa.html || true
    - newman run tests/integration/qa_integration/AuthorizationService_CA.json -e tests/integration/qa_integration/QA_Environment_CA.json  --reporters cli,htmlextra --timeout-request 2500 --reporter-htmlextra-export ./report-ca-qa.html
  allow_failure: true
  artifacts:
    when: always
    paths:
      - ./report-da-qa.html
      - ./report-ca-qa.html
  rules:
    - if: '$CLUSTER_ENVIRONMENT == "spogtest" && $CI_DEFAULT_BRANCH == "master"'
      when: manual
