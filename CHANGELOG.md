The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

# Changelog

### Unreleased

### Added

### Changed

### Removed

### Fixed

## Released
0.0.23 20-06-2025

### Added
[SIM-3186] - Added sim action permissions
### Changed

### Removed

### Fixed

## Released
0.0.22 02-06-2025

### Added
[SIM-3045] - Create role optimization
### Changed
[SIM-3146] - Group SubGroup API changes as per new keycloak
### Removed

### Fixed

## Released
0.0.21 29-05-2025

### Added
[SIM-3107] - Added permission script for connection history data, voice, sms.
### Changed

### Removed

### Fixed

## Released
0.0.20 29-04-2025

### Added
[SIM-3012] - Added script to logout all active users
### Changed

### Removed

### Fixed

## Released
0.0.19 08-04-2025

### Added
[SIM-2948] - Added script to assign permission to CA and DA

### Changed

### Removed

### Fixed

## Released
0.0.18 04-04-2025

### Added
[SIM-2945] - Added script to remove permission from CA and DA

### Changed

### Removed

### Fixed

## Released
0.0.17 02-04-2025

### Added
[SIM-2909] - Added permission `Update Selected MSISDN Records`

### Changed

### Removed

### Fixed

## Released
0.0.16 27-03-2025

### Added

### Changed

### Removed

### Fixed
[SIM-2663] - dynamic_script_update script updated for assigning multiple scopes to resource
[SIM-2660] - bandit and semgrep changes

## Released
0.0.15 17-02-2025

### Added
[SIM-2856] - rate_plan_by_accounts_account_id_changes permission script added

### Changed

### Removed

### Fixed

## Released
0.0.14 30-01-2025

### Added
[SIM-2800] - scope_permission_creation_dynamic_script_in_existing_resource

### Changed

### Removed

### Fixed

## Released
0.0.13 23-12-2024

### Added

### Changed

### Removed

### Fixed
[SIM-2725] - get_group_members_api_fix

## Released
0.0.12 25-07-2024

### Added
[SPOG-1934] - automation-rules-permissions script 22_07_2024_create_automation_permissions.py written
[SPOG-2467] - SPOG-1934 SPOG_automation_rule_authorization_permission
### Changed

### Removed

### Fixed

## Released
0.0.11 17-06-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2428] - timeout-request-error-fix

## Released
0.0.10 13-06-2024

### Added
[SPOG-2291] - Move `View SIM Remains` permission to `SIM Orders`
[SPOG-2282] - Added revoke token API
[SPOG-2414] - Configure GitLab Merge Request Template
[SPOG-1927] - authorization-keycloak-nexus-config

### Changed

### Removed

### Fixed

## Released
0.0.9 21-05-2024

### Added
[SPOG-2291] - Move `View SIM Remains` permission to `SIM Orders`
[SPOG-2282] - Added revoke token API

### Changed

### Removed

### Fixed

## Released
0.0.8 21-05-2024

### Added
[SPOG-2291] - Move View SIM Remains permission to SIM Orders
[SPOG-2282] - Added revoke token API

### Changed

### Removed

### Fixed

## Released
0.0.7 05-04-2024

### Added
[SPOG-2284] - authorization-permission-needs-to-add for get sim details in migration script
[SPOG-2275] - Write a script to create a permission for sim reallocation

### Changed

### Removed

### Fixed

## Released
0.0.6 29-03-2024

### Added
[SPOG-1971] - Add description in ClientAdmin, DistributorAdmin and ReadOnly Roles

### Changed

### Removed

### Fixed

## Released
0.0.5 28-03-2024

### Added
[SPOG-2264] - Script to create Distributor_ReadOnly, Client_ReadOnly and assign permissions for them

### Changed

### Removed

### Fixed
[SPOG-2270] Create/Update Role APIs takes too long time
[SPOG-2264] - Fixed get api ui permission

## Released
0.0.4 19-03-2024

### Added

### Changed

### Removed
[SPOG-2264] - Remove max 50 character validation from evaluate/scope API

### Fixed

## Released
0.0.3 14-03-2024

### Added
[SPOG-2256] - Assign relevant permissions to ClientAdmin

### Changed
[SPOG-2218] - DistributorAdmin, ClientAdmin role can't be edited

### Removed
[SPOG-2054] - removed dependency from ping, healthy API

### Fixed
[SPOG-2198] - api-modify-resource-scope-validation

## Released
0.0.1 29-02-2024

### Added
[SPOG-1742] - api-create-permission-endpoint
[SPOG-1814] - pytest for successful pipeline
[SPOG-1744] - Update resource and scope
[SPOG-1745] - Get Resource and scope by ID and complete list  | get permission by scope id
[SPOG-1747] - get-user-permission-scope-rights
[SPOG-1746] - get-group-roles
[SPOG-1764] - get permission by scope id
[SPOG-1773] - create-role-api
[SPOG-1774] - api-get-roles
[SPOG-1812] - api-assign-roles-to-group
[SPOG-1844] - api-get-role-policy-permission-list
[SPOG-1840] - Get client roles
[SPOG-1811] - api-check-role-exists-or-not
[SPOG-1870] - api-get-policy
[SPOG-1841] - Get policies by permission id
[SPOG-1867] - apply pagination to get api endpoit
[SPOG-1880] - api-get-roles-by-id
[SPOG-1890] - api-get-user
[SPOG-1817] - api-delete-roles-policies
[SPOG-1913] - api-delete-resource
[SPOG-1924] - Add-migration-script-to-map-role-and-group
[SPOG-1916] - Create/update Role API added
[SPOG-1954] - Assign roles to group while creating the role
[SPOG-1979] - Set attributes to default roles
[SPOG-2025] - Add ping, healthy and custom-metric endpoints
[SPOG-2102] - Auth from keycloak
[SPOG-2102] - Create Resource API execution change
[SPOG-2099] - Authorization endpoints and validation changes

### Changed
[SPOG-1866] - Refactor get resource API Endpoint | Update resource scope 
[SPOG-1909] - Add roles and groups to user me/all response
[SPOG-1919] - api-roles-wise-users-count-for-all-roles-and-group-roles
[SPOG-1966] - Filter roles and remove unwated roles from get roles
[SPOG-1976] - Restrict to update and delete default roles
[SPOG-1841] - api-get-existing-policy-list-for-the-permission
[SPOG-1976] - default-role-update-delete-not-allowed-and-create-role-permission-check
[SPOG-2121] - `created_by` key added into Get Role, Create Role APIs response None or user email

### Removed

### Fixed
[SPOG-1748] - Code structurization
[SPOG-2137] - Get role wise user count

## Released

### Added

### Changed

### Removed

### Fixed

## Released


## Added


## Released

### Added

### Changed

### Removed

### Fixed
