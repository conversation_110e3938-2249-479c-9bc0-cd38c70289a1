[build-system]
requires = ["poetry-core>=1.2.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "authorization-api"
version = "0.1.0"
description = "Authorization API"
authors = ["Nextgen"]
readme = "README.md"
packages = [{ include = "src" }]

[[tool.poetry.source]]
name = "nexus"
url = "https://ci-user:<EMAIL>/repository/pypi/simple/"

[tool.poetry.extras]
test = ["pytest", "pytest-cov"]

[tool.poetry.dependencies]
python = "^3.10"
requests = "^2.28.1"
fastapi = ">=0.68.0,<0.69.0"
starlette = "0.14.2"
uvloop = "^0.17.0"
uvicorn = ">=0.13.3,<0.14.0"
gunicorn = "^20.1.0"
httptools = "^0.5.0"
attrs = "^21.2.0"
setuptools = "<67"

python-multipart = ">=0.0.5,<0.1.0"
pydantic = { version = "1.9.2", extras = ["dotenv", "email"] }
# https://github.com/samuelcolvin/pydantic/issues/4009
email-validator = "1.1.1"
yarl = "1.7.2"
pyyaml = "6.0"

SQLAlchemy = { version = ">=1.4.25,<1.5.0", extras = ["mypy"] }

typer = "0.7.0"
tenacity = "8.0.1"

oso = "0.23.0"
sqlalchemy-oso = "0.23.0"

# OpenTelemetry SDK
opentelemetry-api = "1.12.0rc1"
opentelemetry-sdk = "1.12.0rc1"
opentelemetry-proto = "1.11.1"
opentelemetry-distro = "0.31b0"
opentelemetry-util-http = "0.31b0"
opentelemetry-semantic-conventions = "0.31b0"
protobuf = "3.20.0"

# OpenTelemetry instrumentation
opentelemetry-instrumentation = "0.31b0"
opentelemetry-instrumentation-asgi = "0.31b0"
opentelemetry-instrumentation-httpx = "0.31b0"
opentelemetry-instrumentation-fastapi = "0.31b0"
opentelemetry-instrumentation-logging = "0.31b0"

# Private packages
nv2-keycloak-client = "0.9.18"
nv2-pkg-py-authn = "0.0.7"
prometheus-client = "^0.19.0"
prometheus-fastapi-instrumentator = "^6.1.0"

[tool.poetry.group.test]
optional = true

[tool.poetry.group.test.dependencies]
pytest = "^7.1.3"
pytest-lazy-fixture = "0.6.3"
pytest-env = "^0.6.2"
requests = "^2.28.1"
coverage = "^6.5.0"
flake8 = "^5.0.4"
twine = "^4.0.1"
mccabe = "^0.7.0"
mypy = "^0.982"
pylint = "^2.15.4"
tox = "^3.26.0"
factory-boy = "^3.2.1"
pytest-subtests = "^0.8.0"
lxml = "^4.9.1"
types-PyYAML = "^6.0.12"
bandit = "^1.7.5"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
pre-commit = "^2.20.0"


[tool.mypy]
files = "src/app"
plugins = "sqlalchemy.ext.mypy.plugin,pydantic.mypy"

[[tool.mypy.overrides]]
module = "uvicorn"
ignore_missing_imports = "True"

[[tool.mypy.overrides]]
module = "sqlalchemy_oso"
ignore_missing_imports = "True"

[tool.isort]
profile = "black"
known_third_party = ["alembic"]

[tool.pytest.ini_options]
pythonpath = "src"
