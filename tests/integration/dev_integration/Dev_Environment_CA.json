{"id": "e5c89a44-d62e-4907-ad75-79b231959c57", "name": "Dev_Environment_CA", "values": [{"key": "TokenUrl", "value": "https://sso.dev.spogconnected.com/auth/realms/dev-connected-platform/protocol/openid-connect/token", "type": "default", "enabled": true}, {"key": "BaseURL", "value": "https://dev.spogconnected.com", "type": "default", "enabled": true}, {"key": "IMSI", "value": "***************", "type": "default", "enabled": true}, {"key": "IMSI_1", "value": "***************", "type": "default", "enabled": true}, {"key": "month", "value": "2024-01", "type": "default", "enabled": true}, {"key": "account_id", "value": "18", "type": "default", "enabled": true}, {"key": "invoice_id", "value": "", "type": "default", "enabled": true}, {"key": "rateplanid", "value": "", "type": "any", "enabled": true}, {"key": "AccountId", "value": "", "type": "any", "enabled": true}, {"key": "Name", "value": "", "type": "default", "enabled": true}, {"key": "Client_id", "value": "organization-management", "type": "default", "enabled": true}, {"key": "Client_secret", "value": "d3df5208-c809-4063-bfba-c2254ebefd2f", "type": "default", "enabled": true}, {"key": "Grant_type", "value": "password", "type": "default", "enabled": true}, {"key": "Username", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "Password", "value": "8BEAB7&$@*ae6D8A23", "type": "default", "enabled": true}, {"key": "fromDate", "value": "2024-07-01", "type": "any", "enabled": true}, {"key": "toDate", "value": "2024-07-30", "type": "any", "enabled": true}, {"key": "Adjustment_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date", "value": "", "type": "default", "enabled": true}, {"key": "Organisation_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date_Future", "value": "", "type": "default", "enabled": true}, {"key": "Adjustment date", "value": "", "type": "any", "enabled": true}, {"key": "random_number", "value": "", "type": "any", "enabled": true}, {"key": "Random_number", "value": "", "type": "any", "enabled": true}, {"key": "contractEndDate", "value": "", "type": "any", "enabled": true}, {"key": "Token_distributor", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIzelVrNUxjSnF3SWY4UDdnVVFad1B4MlYyQXpuX1RNM3Y0ZXl5VHVXOWhvIn0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Iy7kGRM5yVom9Exc4mrXvLJj6DB7HSp0URaSFTEh-0_FXGi_pKDp6kXX93VxMWP8jZ5r7rVkodsJscajuG2d7A1SHG1GKwX7PIPgk6Hs_zR1b2UOoWN_B-ujU7tYbfSffB9q8NQJFPaZuOMftYjH_jSb3oLz3d8hWpEZi1Ec-Ax01PZ-7jFN5wEZNo4ki6cmY5jxlg6DlBwyenbHKasrN7N1Dtm-Uoe2lvYSnuQI6VDpw12z2M9ZvYNTt_xnjzjZ6DQb68cDKCdH_w0fapgAr8mEoMPQ5YC0lvjKgnAbUv9X8wSjRUKJO31utw302P8gd7X9cljGv-jkyoxep3PZKw", "type": "any", "enabled": true}, {"key": "payment_term", "value": "", "type": "default", "enabled": true}, {"key": "sim_charge", "value": "", "type": "default", "enabled": true}, {"key": "account_id_reallocate", "value": "7149", "type": "default", "enabled": true}, {"key": "rateplan_id_reallocate", "value": "1822", "type": "default", "enabled": true}, {"key": "ICCID", "value": "", "type": "default", "enabled": true}, {"key": "imsi_reallocate", "value": "", "type": "default", "enabled": true}, {"key": "ActiveSIM_ID", "value": "7", "type": "default", "enabled": true}, {"key": "ActiveSIM_Month", "value": "2024-03", "type": "default", "enabled": true}, {"key": "group_path", "value": "Nextgen Clearing", "type": "default", "enabled": true}, {"key": "search_value", "value": "DistributorAdmin", "type": "default", "enabled": true}, {"key": "group_id", "value": "", "type": "any", "enabled": true}, {"key": "resource_name", "value": "", "type": "any", "enabled": true}, {"key": "resource_scopes_name", "value": "", "type": "any", "enabled": true}, {"key": "resource_scopes_display_name", "value": "", "type": "any", "enabled": true}, {"key": "resource_id", "value": "", "type": "any", "enabled": true}, {"key": "resource_scopes", "value": "", "type": "any", "enabled": true}, {"key": "owner", "value": "", "type": "any", "enabled": true}, {"key": "role_id", "value": "", "type": "any", "enabled": true}, {"key": "role_name", "value": "", "type": "any", "enabled": true}, {"key": "discription", "value": "", "type": "any", "enabled": true}, {"key": "permission_id", "value": "", "type": "any", "enabled": true}, {"key": "account_id_get_imsi", "value": "7", "type": "default", "enabled": true}, {"key": "Carrier_Type1", "value": "GBRME", "type": "default", "enabled": true}, {"key": "Carrier_Type2", "value": "GBRVF", "type": "default", "enabled": true}, {"key": "Carrier_Type3", "value": "GBRCN", "type": "default", "enabled": true}, {"key": "rule_uuid", "value": "20fae64b-50e6-4df4-87b4-a5ab402f8269", "type": "any", "enabled": true}, {"key": "Rule_Name", "value": "srgv7lqidt", "type": "any", "enabled": true}, {"key": "Data_Volume", "value": 3413, "type": "any", "enabled": true}, {"key": "Random_Email", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "notification_id", "value": "669a03e1ab7f6f5476b10270", "type": "any", "enabled": true}, {"key": "cdr_ID", "value": "*********", "type": "any", "enabled": true}, {"key": "rule_ID", "value": "7d2f8782-9a1b-40bd-88a7-e576906de23d", "type": "any", "enabled": true}, {"key": "imsi_notification", "value": "**************", "type": "any", "enabled": true}, {"key": "usage_limit_notification", "value": 504, "type": "any", "enabled": true}, {"key": "month_notification", "value": "2024-07", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-12-06T09:48:23.844Z", "_postman_exported_using": "Postman/11.21.0"}