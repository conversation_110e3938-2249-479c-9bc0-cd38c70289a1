{"id": "761bec6d-d6f4-4c40-b5a1-5890787ac5e8", "name": "Dev_Environment_DA", "values": [{"key": "TokenUrl", "value": "https://sso.dev.spogconnected.com/auth/realms/dev-connected-platform/protocol/openid-connect/token", "type": "default", "enabled": true}, {"key": "BaseURL", "value": "https://dev.spogconnected.com", "type": "default", "enabled": true}, {"key": "IMSI", "value": "***************", "type": "default", "enabled": true}, {"key": "IMSI_1", "value": "***************", "type": "default", "enabled": true}, {"key": "month", "value": "2024-10", "type": "default", "enabled": true}, {"key": "account_id", "value": "95", "type": "default", "enabled": true}, {"key": "invoice_id", "value": "", "type": "default", "enabled": true}, {"key": "rateplanid", "value": null, "type": "any", "enabled": true}, {"key": "AccountId", "value": 10617, "type": "any", "enabled": true}, {"key": "Name", "value": "ldarox3aak", "type": "default", "enabled": true}, {"key": "Client_id", "value": "organization-management", "type": "default", "enabled": true}, {"key": "Client_secret", "value": "d3df5208-c809-4063-bfba-c2254ebefd2f", "type": "default", "enabled": true}, {"key": "Grant_type", "value": "password", "type": "default", "enabled": true}, {"key": "Username", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "Password", "value": "Dev@Test1234", "type": "default", "enabled": true}, {"key": "fromDate", "value": "2024-10-01", "type": "any", "enabled": true}, {"key": "toDate", "value": "2024-10-31", "type": "any", "enabled": true}, {"key": "Adjustment_id", "value": null, "type": "default", "enabled": true}, {"key": "Billing_cycle_date", "value": "2024-10", "type": "default", "enabled": true}, {"key": "Organisation_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date_Future", "value": "", "type": "default", "enabled": true}, {"key": "Adjustment date", "value": "2024-10-01", "type": "any", "enabled": true}, {"key": "random_number", "value": "***************", "type": "any", "enabled": true}, {"key": "Random_number", "value": "", "type": "any", "enabled": true}, {"key": "contractEndDate", "value": "2024-10-31", "type": "any", "enabled": true}, {"key": "payment_term", "value": "992", "type": "default", "enabled": true}, {"key": "sim_charge", "value": "56683068404134996308", "type": "default", "enabled": true}, {"key": "account_id_reallocate", "value": "7149", "type": "default", "enabled": true}, {"key": "rateplan_id_reallocate", "value": "1822", "type": "default", "enabled": true}, {"key": "ICCID", "value": null, "type": "default", "enabled": true}, {"key": "imsi_reallocate", "value": "***************", "type": "default", "enabled": true}, {"key": "ActiveSIM_ID", "value": "7", "type": "default", "enabled": true}, {"key": "ActiveSIM_Month", "value": "2024-10", "type": "default", "enabled": true}, {"key": "group_path", "value": "Nextgen Clearing", "type": "default", "enabled": true}, {"key": "search_value", "value": "DistributorAdmin", "type": "default", "enabled": true}, {"key": "group_id", "value": "4e42f62b-9190-464c-a753-061cfea6590b", "type": "any", "enabled": true}, {"key": "resource_name", "value": "52vf93upqq", "type": "any", "enabled": true}, {"key": "resource_scopes_name", "value": "xjrxntc", "type": "any", "enabled": true}, {"key": "resource_scopes_display_name", "value": "vma9lse", "type": "any", "enabled": true}, {"key": "resource_id", "value": "6c52f149-cc74-4481-8edb-e5b8e72bd8e4", "type": "any", "enabled": true}, {"key": "resource_scopes", "value": "hytgfk5", "type": "any", "enabled": true}, {"key": "owner", "value": "ks0xh", "type": "any", "enabled": true}, {"key": "role_id", "value": "25894cc2-cd36-42ce-a48e-addde85bd467", "type": "any", "enabled": true}, {"key": "role_name", "value": "w0lqlp2fwc", "type": "any", "enabled": true}, {"key": "discription", "value": "8oo73kgiuyvcvsn2k8zd", "type": "any", "enabled": true}, {"key": "permission_id", "value": "ec7fc3f6-e1e2-493d-9b0a-5912cdf31754", "type": "any", "enabled": true}, {"key": "account_id_get_imsi", "value": "7", "type": "default", "enabled": true}, {"key": "Carrier_Type1", "value": "GBRME", "type": "default", "enabled": true}, {"key": "Carrier_Type2", "value": "GBRVF", "type": "default", "enabled": true}, {"key": "Carrier_Type3", "value": "GBRCN", "type": "default", "enabled": true}, {"key": "resource_scopes1", "value": "lt5kx6xj9d", "type": "any", "enabled": true}, {"key": "scope_id", "value": "376075a4-3d43-47d9-94b3-256cbdf8eec8", "type": "any", "enabled": true}, {"key": "permission_name", "value": "83zgxwnp8aqq", "type": "any", "enabled": true}, {"key": "MSISDN", "value": null, "type": "any", "enabled": true}, {"key": "Token_distributor ", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIzelVrNUxjSnF3SWY4UDdnVVFad1B4MlYyQXpuX1RNM3Y0ZXl5VHVXOWhvIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.a3hsQkxoTbeFfFGRmIDmG6kMAmlANOkmfHtZ0M6fgwwAytleMLxBFlTeTGRde_-f34DWedLkY2Dz141DNuhyJhofcMI6NllCjCtXFMdohwxAx5j06wykvG7T70x_j1lWfwk4lpL6lMACnl_tKtXn_oZVfS0M0otoVswXo9oPhZ8zmmzp0yCQ_jFdgaD719CyyzpAQTTZ-DiY2Prjr8HBZT7NY0aMSJqIpn1YRwZFgNDASt6snOn8YCWu9S4G7pO39-orNalbUTQHwi9-99F4sEV_pGFtXaFUzKhWDRqXcJYSs3bUSihMweF18T15P0H7d9fd_nOIP5PYoD6P4ojeQg", "type": "any", "enabled": true}, {"key": "Token_distributor", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIzelVrNUxjSnF3SWY4UDdnVVFad1B4MlYyQXpuX1RNM3Y0ZXl5VHVXOWhvIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.F37rWagxprfo3Zpe9kycgAjenUEmpAS0b8O9WKpBp4a7UvS_NBj5jYRUMr0i3B_xpuv_nyIyNt6HlPsw1tE-fLR0Crh8eowaXN0_LXH2YFfTnhh6m03vOfac1-nWkHG1fd5_VBMmeT7bmVJ3-Z4MIhqmAXhT8xx-zzAH4cWvvZzWwuDWBHouV1M7CVTIstNT6WZXdYLtSl-XQc73UKG4n5bpoSjcihtZs56hdkEI2bvUO9felhwSiBhyoe01iwEE7zI-FeKokkVxtxmn9txHROllRbhykIIrGbaiWanXhHu4a-pGFusvNYQuEis0HnyTG4Auz9LJQFGG0gxR7Ikw-Q", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-12-06T09:48:09.968Z", "_postman_exported_using": "Postman/11.21.0"}