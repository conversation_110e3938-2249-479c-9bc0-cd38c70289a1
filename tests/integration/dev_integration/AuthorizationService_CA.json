{"info": {"_postman_id": "72d37483-2d9a-49fa-87e5-c5db236b30ee", "name": "AuthorizationService_CA", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29298941", "_collection_link": "https://nextgenclearing.postman.co/workspace/QA-_Team_SPOG~66755c34-5aed-4dcf-af75-9956e2650e16/collection/29298941-72d37483-2d9a-49fa-87e5-c5db236b30ee?action=share&source=collection_link&creator=29298941"}, "item": [{"name": "Group", "item": [{"name": "Get Group and Subgroup", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Groups - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}, {"name": "Get Groups - Invalid Url", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/grouprrere", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "grouprrere"]}}, "response": []}, {"name": "Get Groups - response body format", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response body has 'results' key\", function () {\r", "    const responseBody = pm.response.json();\r", "    pm.expect(responseBody).to.have.property('result');\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}, {"name": "Get Groups - Response time is less than 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}, {"name": "Get Groups - Verify object valid key fields", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Each result object has id, name, and path fields\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData.result).to.be.an('array');\r", "    responseData.result.forEach(function(result) {\r", "        pm.expect(result).to.have.property('id');\r", "        pm.expect(result).to.have.property('name');\r", "        pm.expect(result).to.have.property('path');\r", "    });\r", "});\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}, {"name": "Get Groups - Verify subGroup Array in response", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Each subGroup object contains a subGroups array\", function () {\r", "    const responseData = pm.response.json();\r", "    \r", "    pm.expect(responseData.result).to.be.an('array');\r", "    responseData.result.forEach(function(group) {\r", "        pm.expect(group.subGroups).to.be.an('array');\r", "    });\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}, {"name": "Get Groups - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", "\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}, {"name": "Get Groups - Status code and code name string", "event": [{"listen": "test", "script": {"exec": ["", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Status code name 'OK'\", function () {", "    pm.response.to.have.status(\"OK\");", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}, "description": "\nThis endpoint retrieves a list of authorization groups. The response will include the ID, name, and path of each group, as well as any subgroups within each group.\n\nThe response will be in JSON format with a status code of 200.\n"}, "response": []}, {"name": "Get Groups - Valid response schema", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Schema is valid\", function () {\r", "  var schema = {\r", "    type: \"object\",\r", "    properties: {\r", "      result: {\r", "        type: \"array\",\r", "        items: {\r", "          type: \"object\",\r", "          properties: {\r", "            id: { type: \"string\" },\r", "            name: { type: \"string\" },\r", "            path: { type: \"string\" },\r", "            subGroups: {\r", "              type: \"array\",\r", "              items: {\r", "                type: \"object\",\r", "                properties: {\r", "                  id: { type: \"string\" },\r", "                  name: { type: \"string\" },\r", "                  path: { type: \"string\" },\r", "                  subGroups: {\r", "                    type: \"array\",\r", "                  },\r", "                },\r", "              },\r", "            },\r", "          },\r", "        },\r", "      },\r", "    },\r", "    required: [\"result\"],\r", "  };\r", "\r", "  var response = pm.response.json();\r", "  pm.expect(tv4.validate(response, schema)).to.be.true;\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}, {"name": "Authorization - Invalid token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "invalid", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}, {"name": "Authorization - <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}, {"name": "Authorization - Without Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}, {"name": "Header - Content type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Content-Type is present\", function () {\r", "   pm.response.to.have.header(\"Content-Type\");\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}]}]}, {"name": "User", "item": [{"name": "Get Users", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Users - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"]}}, "response": []}, {"name": "Get Users - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user"]}}, "response": []}, {"name": "Get Users - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"]}}, "response": []}, {"name": "Get Users - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"]}}, "response": []}, {"name": "Get Users - Response code name string \"ok\"", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Status code name 'OK'\", function () {\r", "    pm.response.to.have.status(\"OK\");\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Get Users - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"]}}, "response": []}, {"name": "Get Users - Page number valid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Get Users - Page number Alphanum", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=alpha1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": "alpha1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Get Users - Page Size Alphanum", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=1&page_size=10test", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10test"}]}}, "response": []}, {"name": "Get Users - Page & Page Size Invalid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=1a&page_size=******", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": "1a"}, {"key": "page_size", "value": "******"}]}}, "response": []}, {"name": "Get Users - Page Maximum 100000", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=100000&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": "100000"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Get Users - Page Minimum 0", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=0&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": "0"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Get Users - Blank Page", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Get Users - Page valid page size max", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=1&page_size=10000000", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10000000"}]}}, "response": []}, {"name": "Get Users - Page valid page size min", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=1&page_size=0", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "0"}]}}, "response": []}, {"name": "Get Users - Page valid page size blank", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=1&page_size=", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": ""}]}}, "response": []}, {"name": "Get Users - Both field blank", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=&page_size=", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": ""}, {"key": "page_size", "value": ""}]}}, "response": []}, {"name": "Get Users - Page valid & Page Size not multiple of 10", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/users?page=1&page_size=5", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "users"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "5"}]}}, "response": []}]}, {"name": "Get Users Scope", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Users Scopes - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/scope", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "scope"]}}, "response": []}, {"name": "Get Users Scopes - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/scopes", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "scopes"]}}, "response": []}, {"name": "Get Users Scopes  - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/scope", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "scope"]}}, "response": []}, {"name": "Get Users Scopes - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/scope", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "scope"]}}, "response": []}, {"name": "Get Users Scopes  - Response code 200 String \"OK\"", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Status code name 'OK'\", function () {\r", "    pm.response.to.have.status(\"OK\");\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/scope", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "scope"]}}, "response": []}, {"name": "Get Users Scopes  - Token Invalid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "urmila", "type": "string"}, {"key": "username", "value": "urmila", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/scope", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "scope"]}}, "response": []}, {"name": "Get Header - Content Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Content-Type is present\", function () {\r", "   pm.response.to.have.header(\"Content-Type\");\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/scope", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "scope"]}}, "response": []}]}, {"name": "Read Authenticated User", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Read Authenticated User - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/me", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "me"]}}, "response": []}, {"name": "Read Authenticated User - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/mi", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "mi"]}}, "response": []}, {"name": "Read Authenticated User - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/me", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "me"]}}, "response": []}, {"name": "Read Authenticated User - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/me", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "me"]}}, "response": []}, {"name": "Read Authenticated User - Response code name 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Status code name 'OK'\", function () {\r", "    pm.response.to.have.status(\"OK\");\r", "});\r", "\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/me", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "me"]}}, "response": []}, {"name": "Read Authenticated User - Token Invalid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "urmila", "type": "string"}, {"key": "username", "value": "urmila", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/me", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "me"]}}, "response": []}, {"name": "Read Authenticated User - Blank Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/me", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "me"]}}, "response": []}, {"name": "Read Authenticated User - Without Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/me", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "me"]}}, "response": []}]}, {"name": "Get Evaluated User Scope", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get User Evaluate - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluate", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluate"]}}, "response": []}, {"name": "Get User Evaluate - Invalid  URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluated", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluated"]}}, "response": []}, {"name": "Get User Evaluate - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluate", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluate"]}}, "response": []}, {"name": "Get User Evaluate - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluate", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluate"]}}, "response": []}, {"name": "Get User Evaluate - Token Invalid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "urmila", "type": "string"}, {"key": "username", "value": "urmila", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluate", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluate"]}}, "response": []}, {"name": "Get User Evaluate - Blank Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluate", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluate"]}}, "response": []}, {"name": "Get User Evaluate - Verify Response Body", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Body matches string\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"access_token\");\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluate", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluate"]}}, "response": []}]}, {"name": "Evaluate Authorized Scope", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Evaluate Authorized Scope - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"scope\": \"GET/v1/glass/accounts\"\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluate/scope", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluate", "scope"]}}, "response": []}, {"name": "Evaluate Authorized Scope - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"scope\": \"GET/v1/glass/accounts\"\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluat/scpe", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluat", "scpe"]}}, "response": []}, {"name": "Evaluate Authorized Scope - Invalid <PERSON>ope Name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"scope\": \"GET/v1/glas/acounts\"\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluate/scope", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluate", "scope"]}}, "response": []}, {"name": "Evaluate Authorized Scope - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"scope\": \"GET/v1/glas/acounts\"\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluate/scope", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluate", "scope"]}}, "response": []}, {"name": "Evaluate Authorized Scope - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"scope\": \"GET/v1/glass/accounts\"\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluate/scope", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluate", "scope"]}}, "response": []}, {"name": "Evaluate Authorized Scope - Invalid Method Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"scope\": \"GET/v1/glas/acounts\"\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/user/evaluate/scope", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "user", "evaluate", "scope"]}}, "response": []}]}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Get Policy", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Policy - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Get Policy - Invalid Url", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policydsds?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policydsds"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Get Policy - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Params - <PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=invalid&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "invalid"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Params - <PERSON><PERSON> Page", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": ""}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Params - Without page params", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": null, "value": null}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Params - Minimum Page", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": ""}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Params - Maximum Page", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1000000&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1000000"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Params - Invalid Page_size", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=invalid", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "invalid"}]}}, "response": []}, {"name": "Params - Blank Page_size", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": ""}]}}, "response": []}, {"name": "Params - Without Page_size", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "", "value": null}]}}, "response": []}, {"name": "Params - Minimum Page_size", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=0", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "0"}]}}, "response": []}, {"name": "Params - Maximum Page_size", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=1000000000000000", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "1000000000000000"}]}}, "response": []}, {"name": "Get Policy - Without optional key value", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"]}}, "response": []}, {"name": "Get Policy - Response time is less than 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Get Policy - Verify Status code and code name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Status code name has string\", function () {\r", "    pm.response.to.have.status(\"OK\");\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Get Policy - Verify Response Schema", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Schema is valid\", function () {\r", "  var schema = {\r", "    type: \"object\",\r", "    properties: {\r", "      policy: {\r", "        type: \"array\",\r", "        items: {\r", "          type: \"object\",\r", "          properties: {\r", "            id: { type: \"string\" },\r", "            name: { type: \"string\" },\r", "            description: { type: \"string\" },\r", "            type: { type: \"string\" },\r", "            logic: { type: \"string\" },\r", "            decisionStrategy: { type: \"string\" },\r", "            config: {\r", "              type: \"object\",\r", "              properties: {\r", "                roles: { type: \"string\" }\r", "              },\r", "              required: [\"roles\"]\r", "            }\r", "          },\r", "          required: [\"id\", \"name\", \"description\", \"type\", \"logic\", \"decisionStrategy\", \"config\"]\r", "        }\r", "      }\r", "    },\r", "    required: [\"policy\"]\r", "  };\r", "\r", "  var response = pm.response.json();\r", "  pm.expect(tv4.validate(response, schema)).to.be.false;\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Authorization - invalid Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "invalid", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Authorization - <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Authorization - Without Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "Header - verify Content-Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response has content type application/json\", function () {\r", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/json\");\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/policy?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "policy"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}]}]}, {"name": "Resource/Scope/Permission", "item": [{"name": "Get Resource Scope Permission", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Resource Scope Permission - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/resource/scope/permission", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "resource", "scope", "permission"]}}, "response": []}, {"name": "Get Resource Scope Permission - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/resource/scope/permission", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "resource", "scope", "permission"]}}, "response": []}, {"name": "Get Resource Scope Permission - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/resource/scope/permissions", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "resource", "scope", "permissions"]}}, "response": []}, {"name": "Get Resource Scope Permission - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/resource/scope/permission", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "resource", "scope", "permission"]}}, "response": []}, {"name": "Get Resource Scope Permission - Response time < 500 ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/resource/scope/permission", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "resource", "scope", "permission"]}}, "response": []}, {"name": "Get Resource Scope Permission - Invalid Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/resource/scope/permission", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "resource", "scope", "permission"]}}, "response": []}, {"name": "Get Resource Scope Permission - No Auth Case", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/resource/scope/permission", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "resource", "scope", "permission"]}}, "response": []}]}]}, {"name": "Realm_role", "item": [{"name": "Create Role", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Roles - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"role_id\", jsonData.results[0].id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Get Permissions Role Id - 200", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"permission_id\", jsonData.permission[0]);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/{{role_id}}/permission", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "{{role_id}}", "permission"]}}, "response": []}, {"name": "Create Role - 201 Created", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"role_id\", jsonData.id);\r", "\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1) {\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('role_name', randomString(10));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/roleeee", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "<PERSON>eee"]}}, "response": []}, {"name": "Create Role - Blank Role Name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Without Role Name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Only Space with Created Role", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"  \",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Only Special Character with Created Role", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"#%$^&*$\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Blank Description Name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1) {\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('role_name', randomString(10));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Without Description Name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1) {\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('role_name', randomString(10));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Without Attributes", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1) {\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('role_name', randomString(10));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Blank RoleGroup", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Without RoleGroup", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Blank Permission Created Role", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Without Permission Created Role", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\"\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - 401 Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Create Role - 409 Conflict", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 409\", function () {\r", "    pm.response.to.have.status(409);\r", "});\r", "\r", "    "], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}]}, {"name": "Get Realm Role", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Role - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Get Role - Invalid <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/roleee", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "roleee"]}}, "response": []}, {"name": "Get Role - Response time is less than 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Get Role - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", "\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Get Role - Status code and code name string", "event": [{"listen": "test", "script": {"exec": ["", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Status code name 'OK'\", function () {", "    pm.response.to.have.status(\"OK\");", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}, "description": "\nThis endpoint retrieves a list of authorization groups. The response will include the ID, name, and path of each group, as well as any subgroups within each group.\n\nThe response will be in JSON format with a status code of 200.\n"}, "response": []}, {"name": "Get Role - Invalid token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "invalid", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Get Role - <PERSON><PERSON>ken", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Get Role - Without Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"]}}, "response": []}, {"name": "Header - Content type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Content-Type is present\", function () {\r", "   pm.response.to.have.header(\"Content-Type\");\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/group", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "group"]}}, "response": []}, {"name": "Get Role - Page, PageSize", "event": [{"listen": "test", "script": {"exec": ["", "pm.test(\"Response status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role?page=1&page_size=10", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}, {"key": "search", "value": "", "disabled": true}]}}, "response": []}, {"name": "Get Role - with Invalid Search", "event": [{"listen": "test", "script": {"exec": ["", "pm.test(\"Response status code is 404\", function () {", "    pm.response.to.have.status(404);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role?search=\"Hello\"", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role"], "query": [{"key": "search", "value": "\"Hello\""}]}}, "response": []}]}, {"name": "Get Role By ID", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Role By ID - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}"]}}, "response": []}, {"name": "Get Role By ID - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}"]}}, "response": []}, {"name": "Get Role By ID - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/re/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "re", "{{role_id}}"]}}, "response": []}, {"name": "Get Role By ID - Without Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", ""]}}, "response": []}, {"name": "Get Role By ID - Blank Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{}"]}}, "response": []}, {"name": "Get Role By ID - Invalid role_id", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{454545}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{454545}"]}}, "response": []}, {"name": "Get Role By ID - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}"]}}, "response": []}, {"name": "Get Role By ID - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}"]}}, "response": []}]}, {"name": "Update Role", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Update Role - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1) {\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('role_name', randomString(10));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}"]}}, "response": []}, {"name": "Update Role - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}"]}}, "response": []}, {"name": "Update Role - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/re/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "re", "{{role_id}}"]}}, "response": []}, {"name": "Update Role - Without Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role/", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", ""]}}, "response": []}, {"name": "Update Role - Blank Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role/{}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{}"]}}, "response": []}, {"name": "Update Role - Invalid Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role/{4545454}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{4545454}"]}}, "response": []}, {"name": "Update Role - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1) {\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('role_name', randomString(10));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}}"]}}, "response": []}, {"name": "Update Role - Unathorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\"{{permission_id}}\"]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}"]}}, "response": []}]}, {"name": "Delete Role by ID", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Delete Role - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}"]}}, "response": []}, {"name": "Delete Role - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}"]}}, "response": []}, {"name": "Delete Role - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/authorization/re/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "re", "{{role_id}}"]}}, "response": []}, {"name": "Delete Role - Without Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", ""]}}, "response": []}, {"name": "Delete Role - Blank Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{}"]}}, "response": []}, {"name": "Delete Role - Invalid Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{4jbg656}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{4jbg656}"]}}, "response": []}, {"name": "Delete Role - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}"]}}, "response": []}, {"name": "Delete Role - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/authorization/role/{{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "authorization", "role", "{{role_id}}"]}}, "response": []}]}]}]}