{"id": "7d7d6f26-4b35-4173-ba0f-44ec659db62e", "name": "QA_Environment_DA", "values": [{"key": "TokenUrl", "value": "https://sso.test.spogconnected.com/auth/realms/test-connected-platform/protocol/openid-connect/token", "type": "default", "enabled": true}, {"key": "BaseURL", "value": "https://test.spogconnected.com", "type": "default", "enabled": true}, {"key": "IMSI", "value": "***************", "type": "default", "enabled": true}, {"key": "IMSI_1", "value": "***************", "type": "default", "enabled": true}, {"key": "month", "value": "2025-05", "type": "default", "enabled": true}, {"key": "account_id", "value": "1", "type": "default", "enabled": true}, {"key": "invoice_id", "value": 462, "type": "default", "enabled": true}, {"key": "rateplanid", "value": 295, "type": "any", "enabled": true}, {"key": "AccountId", "value": 818, "type": "any", "enabled": true}, {"key": "Name", "value": "iozqqyukh6", "type": "default", "enabled": true}, {"key": "Client_id", "value": "organization-management", "type": "secret", "enabled": true}, {"key": "Client_secret", "value": "d3df5208-c809-4063-bfba-c2254ebefd2f", "type": "secret", "enabled": true}, {"key": "Grant_type", "value": "password", "type": "secret", "enabled": true}, {"key": "Username", "value": "<EMAIL>", "type": "secret", "enabled": true}, {"key": "Password", "value": "P@ssw0rd1", "type": "secret", "enabled": true}, {"key": "fromDate", "value": "2025-05-01", "type": "any", "enabled": true}, {"key": "toDate", "value": "2025-05-30", "type": "any", "enabled": true}, {"key": "Adjustment_id", "value": 106, "type": "default", "enabled": true}, {"key": "Billing_cycle_date", "value": "2025-04", "type": "default", "enabled": true}, {"key": "Organisation_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date_Future", "value": "2024-09", "type": "default", "enabled": true}, {"key": "Adjustment date", "value": "2025-05-01", "type": "any", "enabled": true}, {"key": "random_number", "value": "911451529429790", "type": "any", "enabled": true}, {"key": "Random_number", "value": "", "type": "any", "enabled": true}, {"key": "contractEndDate", "value": "2025-05-31", "type": "any", "enabled": true}, {"key": "payment_term", "value": "3", "type": "any", "enabled": true}, {"key": "sim_charge", "value": "61655680746555943715", "type": "any", "enabled": true}, {"key": "group_path", "value": "Nextgen Clearing", "type": "default", "enabled": true}, {"key": "resource_name", "value": "f0zmjdqyhk", "type": "any", "enabled": true}, {"key": "resource_id", "value": "e4378c75-7d2a-4886-aae4-7b41f69c3273", "type": "any", "enabled": true}, {"key": "role_id", "value": "a8554321-56ea-4859-aed2-317368d794fd", "type": "any", "enabled": true}, {"key": "discription", "value": "k0e5tirmbyngqr4v9x5w", "type": "any", "enabled": true}, {"key": "permission_id", "value": "52df2400-b994-4875-bcbe-7bd814959704", "type": "default", "enabled": true}, {"key": "search_value", "value": "DistributorAdmin", "type": "default", "enabled": true}, {"key": "role_name", "value": "0aah91pau2", "type": "default", "enabled": true}, {"key": "resource_scopes_name", "value": "sk15j3b", "type": "default", "enabled": true}, {"key": "resource_scopes_display_name", "value": "6ywv1k3", "type": "any", "enabled": true}, {"key": "resource_scopes", "value": "8wftblp", "type": "any", "enabled": true}, {"key": "permission_name", "value": "xegew253ff1m", "type": "default", "enabled": true}, {"key": "scope_id", "value": "bc3ffb33-a06d-48e4-9803-77ac506fe8af", "type": "default", "enabled": true}, {"key": "group_id", "value": "99a7fdc4-39df-4cd2-a334-f6572fe9c463", "type": "any", "enabled": true}, {"key": "owner", "value": "aulfx", "type": "any", "enabled": true}, {"key": "account_id_reallocate", "value": "15", "type": "default", "enabled": true}, {"key": "rateplan_id_reallocate", "value": "29", "type": "default", "enabled": true}, {"key": "ICCID", "value": "8944538532046590109", "type": "default", "enabled": true}, {"key": "imsi_reallocate", "value": "**************", "type": "default", "enabled": true}, {"key": "ActiveSIM_ID", "value": "1", "type": "default", "enabled": true}, {"key": "ActiveSIM_Month", "value": "2025-05", "type": "default", "enabled": true}, {"key": "account_id_get_imsi", "value": "163", "type": "default", "enabled": true}, {"key": "Carrier_Type1", "value": "GBRME", "type": "default", "enabled": true}, {"key": "Carrier_Type2", "value": "GBRVF", "type": "default", "enabled": true}, {"key": "Carrier_Type3", "value": "GBRCN", "type": "default", "enabled": true}, {"key": "resource_scopes1", "value": "ast3ktw1if", "type": "any", "enabled": true}, {"key": "MSISDN", "value": "***************", "type": "default", "enabled": true}, {"key": "file_key", "value": ["temp_file_key", "temp_file_key"], "type": "any", "enabled": true}, {"key": "Rule_Name", "value": "a8t5fx95lz", "type": "any", "enabled": true}, {"key": "Data_Volume", "value": 5760, "type": "any", "enabled": true}, {"key": "rule_uuid", "value": "1c815b1d-faff-4c99-8fe9-cd5fd29ab739", "type": "any", "enabled": true}, {"key": "notification_id", "value": "680a2082ee71dc4555a814cb", "type": "any", "enabled": true}, {"key": "work_id", "value": "67fcfeab549e7f47f0230652", "type": "any", "enabled": true}, {"key": "month_adt", "value": "2025-04", "type": "any", "enabled": true}, {"key": "Random_Email", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "dataVolume", "value": "null", "type": "any", "enabled": true}, {"key": "rule_ID", "value": "c3d0f796-18cf-4d51-bef3-1e51a6d24780", "type": "any", "enabled": true}, {"key": "IMSI_notification", "value": "234588570010012", "type": "any", "enabled": true}, {"key": "usage_limit_notification", "value": 5, "type": "any", "enabled": true}, {"key": "month_notification", "value": "2024-12", "type": "any", "enabled": true}, {"key": "ICCID_notification", "value": "8944538532046590125", "type": "default", "enabled": true}, {"key": "MSISDN_notification", "value": "883200000110322", "type": "default", "enabled": true}, {"key": "ModelID", "value": 25, "type": "any", "enabled": true}, {"key": "rateplan_id", "value": 294, "type": "any", "enabled": true}, {"key": "CDR_ID", "value": "680a2076ee71dc4555a814c8", "type": "any", "enabled": true}, {"key": "imsi_notification", "value": "234588570010012", "type": "any", "enabled": true}, {"key": "requestTimeout", "value": "true", "type": "any", "enabled": true}, {"key": "Token_distributor", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJtOTZBd05DbUVkWGdZVGV5VFdBTDV0SXBnQkwzOW83SVI5dUVlVkx1eEtjIn0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hvyqCUYnvU2iNfyQQEXHt06WaMRYIDI4a1K2BVV0wLGQfdoBtaCNqq8n18WDc6lJArRzaKD05hp0rFUW8bPN1NNpggdNFP2xX7_w2YolK0bYpzvZ1J98cWELaQ0Y3TW48nUquVuAtK1qjKxupyeH-fa6WboIFsPaVUeGDXdG7AiHlDlNrM_qqFXCptnh4nvKL_yMWCm_vA3Cdd2aak6JqJXO_JCsBTvNMpoQAH1rTrWTf3P5rPXk4pUpjVt-rpYufVh195HYNIq2cr3hamEZNxWWsREGcYjz7neu-thWQAxB4AcBvuwDE9VvuhP-913oOaBw_uSF5bbvEMVzeHMXvw", "type": "secret", "enabled": true}, {"key": "randomTime", "value": "14:30:52", "type": "any", "enabled": true}, {"key": "rateplanid_1", "value": 215, "type": "any", "enabled": true}, {"key": "Target_RatePlan", "value": 21, "type": "any", "enabled": true}, {"key": "Source_RatePlan", "value": 82, "type": "any", "enabled": true}, {"key": "rateplanid_def", "value": 216, "type": "any", "enabled": true}, {"key": "account_id_rp_change", "value": "3", "type": "default", "enabled": true}, {"key": "imsi_rp_change", "value": "***************", "type": "default", "enabled": true}, {"key": "current_hours", "value": "11", "type": "any", "enabled": true}, {"key": "current_minutes", "value": "29", "type": "any", "enabled": true}, {"key": "current_seconds", "value": "01", "type": "any", "enabled": true}, {"key": "Account_id", "value": 815, "type": "default", "enabled": true}, {"key": "rule_category_id", "value": "1", "type": "default", "enabled": true}, {"key": "rule_category_id_2", "value": "4", "type": "default", "enabled": true}, {"key": "Token", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJtOTZBd05DbUVkWGdZVGV5VFdBTDV0SXBnQkwzOW83SVI5dUVlVkx1eEtjIn0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mjIKBjKp5dZfgOBk1xYmc7Og1Rr1LvHwtdBcO_fYM56F6LDX8VnvnZzfbePpaQfMSQOyOyrLyuu5KC3EumWANCbl5iYSrueA6Kk84pM3n4XTBOsuKsc6TeMFI1zyn-AYWDRxQrJSkop22YTqpMK_39mqEKUEBK7bz0Bl0ftOoi8HcfEUY-7qkrQRxmBy9L_Eqad7ouq_YaQAbt13Mf1oWz-9KdSbL2XQvZay0a_nj_2apZdjcUlo6lfHs-JeRbs8o3WLwM0it9Bi2dq2Vq2j2F_AOvJzV75AbTO9BnAziwvNkbmsJUzj5ILv-2qH0VAVxDhZEEvR9QduJxDfdBmC7A", "type": "any", "enabled": true}, {"key": "IMSI_Audit", "value": "234588570010002", "type": "default", "enabled": true}, {"key": "trace_id", "value": "682c117238e28b955e803648", "type": "any", "enabled": true}, {"key": "RequestID", "value": "6835ab737707c1a964d2e73f", "type": "any", "enabled": true}, {"key": "user_id", "value": "4f239986-ad9e-4b02-81e2-b6036f0b3dc5", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-06-02T11:40:40.827Z", "_postman_exported_using": "Postman/11.47.4"}