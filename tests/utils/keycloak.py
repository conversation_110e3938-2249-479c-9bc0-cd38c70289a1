import json
import logging
from pathlib import Path

import httpx

from src.app.core.config import settings


def import_test_realm(
    admin_username: str = "admin",
    admin_password: str = "admin",
):
    realm_data = get_test_realm_data()
    access_token = get_access_token(admin_password, admin_username)
    with httpx.Client() as client:
        response = client.post(
            settings.KEYCLOAK_URL + "/auth/admin/realms",
            headers=dict(authorization=f"Bearer {access_token}"),
            json=realm_data,
        )
        response.raise_for_status()
    return realm_data


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def delete_test_realm(admin_username: str = "admin", admin_password: str = "admin"):
    realm_data = get_test_realm_data()
    realm_name = realm_data["realm"]
    access_token = get_access_token(admin_username, admin_password)
    with httpx.Client() as http_client:
        response = http_client.delete(
            settings.KEYCLOAK_URL + f"/auth/admin/realms/{realm_name}",
            headers=dict(authorization=f"Bearer {access_token}"),
        )
        response.raise_for_status()


def get_access_token(
    username: str,
    password: str,
    realm: str = "master",
    client_id: str = "admin-cli",
) -> str:
    token_path = f"/auth/realms/{realm}/protocol/openid-connect/token"
    with httpx.Client() as http_client:
        response = http_client.post(
            settings.KEYCLOAK_URL + token_path,
            data=dict(
                grant_type="password",
                username=username,
                password=password,
                client_id=client_id,
            ),
        )
        response.raise_for_status()
        token_data = response.json()
        access_token = token_data["access_token"]
        return access_token


def get_test_realm_data():
    filename = "realm.json"
    realm_json_path = Path(__file__).parent.parent / "data" / filename
    realm_data = json.load(realm_json_path.open())
    return realm_data
