import unittest
import uuid
from unittest.mock import MagicMock

from app.adapters.iam.keycloak import KeycloakClient
from app.adapters.iam.schemas import Client<PERSON><PERSON>

role_obj = [
    {
        "id": "340bd034-06af-485b-befc-1b1517ea896b",
        "name": "Test_spog_role",
        "description": "Test_spog_role",
        "composite": False,
        "clientRole": True,
        "containerId": "8c06012b-28ef-4a60-b81a-09d46802a776",
    },
    {
        "id": "d7c90d06-eae8-4cd1-8f2b-88505b5b50b9",
        "name": "uma_protection",
        "description": "Test_spog_role",
        "composite": False,
        "clientRole": True,
        "containerId": "8c06012b-28ef-4a60-b81a-09d46802a776",
    },
]


class SimulatedResponse:
    def __init__(self, data):
        self.data = data

    def json(self):
        return self.data


class TestKeycloakClient(unittest.TestCase):
    def setUp(self):
        self.mock_ka = MagicMock()
        self.keycloak_client = KeycloakClient(ka=self.mock_ka)

    def test_get_roles(self):
        self.mock_ka.clients.get_client_roles = MagicMock(return_value=role_obj)

        roles = self.keycloak_client.get_client_roles()

        for i in range(len(roles)):
            self.assertIsInstance(roles[i], ClientRole)
            self.assertEqual(roles[i].id, uuid.UUID(role_obj[i]["id"]))
            self.assertEqual(roles[i].name, role_obj[i]["name"])
            self.assertEqual(roles[i].description, role_obj[i]["description"])
            self.assertEqual(roles[i].composite, role_obj[i]["composite"])
            self.assertEqual(roles[i].clientRole, role_obj[i]["clientRole"])
            self.assertEqual(roles[i].containerId, role_obj[i]["containerId"])
