import unittest
import uuid
from unittest.mock import Mock

from app.adapters.iam.schemas import PolicyDetailsResponse
from src.app.adapters.iam.keycloak import KeycloakResourcesAPI


class TestResource(unittest.TestCase):
    def test_get_policies_by_permission(self):
        mock_ka = Mock()
        mock_response = [
            {
                "id": "a6c9329c-e6eb-4fdb-9cb5-5019cb3ac143",
                "name": "ClientAdmin",
                "description": "ClientAdmin",
                "type": "role",
                "logic": "POSITIVE",
                "decisionStrategy": "UNANIMOUS",
                "config": {},
            },
            {
                "id": "815a3d0e-f7ba-4892-b7d8-ab2ce520dec0",
                "name": "DistributorAdmin",
                "description": "DistributorAdmin",
                "type": "role",
                "logic": "POSITIVE",
                "decisionStrategy": "UNANIMOUS",
                "config": {},
            },
        ]

        mock_ka.resources_admin.get_policy_by_permission.return_value = mock_response
        keycloak_instance = KeycloakResourcesAPI(ka=mock_ka)

        permission_id = uuid.uuid4()
        policies = keycloak_instance.get_policies_by_permission(permission_id)

        self.assertEqual(len(policies), 2)
        self.assertIsInstance(policies[0], PolicyDetailsResponse)
        self.assertEqual(policies[0].id, "a6c9329c-e6eb-4fdb-9cb5-5019cb3ac143")
        self.assertEqual(policies[0].name, "ClientAdmin")
        self.assertEqual(policies[0].policyType, "role")
        self.assertIsInstance(policies[1], PolicyDetailsResponse)
        self.assertEqual(policies[1].id, "815a3d0e-f7ba-4892-b7d8-ab2ce520dec0")
        self.assertEqual(policies[1].name, "DistributorAdmin")
        self.assertEqual(policies[1].policyType, "role")
