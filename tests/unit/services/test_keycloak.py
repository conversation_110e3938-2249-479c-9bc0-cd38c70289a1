from unittest.mock import <PERSON>Mock, patch

import pytest
from pydantic import <PERSON>UID4

from app import schemas as user_schema
from app.adapters.iam import schemas
from app.adapters.iam.base import AbstractUsersAPI
from app.adapters.iam.exc import (
    AlreadyExist,
    DefaultRoleError,
    GroupNotFound,
    MemberNotFound,
    PermissionNotFound,
    PolicyAlreadyExist,
    PolicyNotFound,
    ResourceNotFound,
    RoleNotFound,
    ScopeNotFound,
    UsersNotFound,
)
from app.adapters.iam.keycloak import (
    InMemoryKeycloakUsersAPI,
    KeycloakClient,
    KeycloakGroupAPI,
    KeycloakIAM,
    KeycloakPermissionsAPI,
    KeycloakResourcesAPI,
    KeycloakRolesAPI,
    KeycloakUsersAPI,
)
from app.api.api_v1.examples import (
    ASSIGN_GROUP_ROLES,
    CREATE_REALM_ROLES,
    CREATE_RESOURCE,
    EVALUATE_SCOPE,
    G<PERSON><PERSON>_ROLES_LIST,
    G<PERSON>UP_SUB_GROUP,
    GROUPS,
    MERGED_SCOPES,
    PAYLOAD_DATA,
    PERMISSION,
    PERMISSION_BY_SCOPE_COMMON_RESPONSE,
    PERMISSION_DATA,
    PERMISSION_LIST,
    PERMISSION_SCOPE_RESPONSE,
    POLICIES_LIST,
    POLICY,
    POLICY_DETAIL_RESPONSE,
    POLICY_PERMISSION,
    POLICY_RESPONSE,
    REALM_ROLES_RESPONSE,
    RESOURCE_LIST,
    RESOURCE_RESPONSE,
    ROLE_WISE_USER_LIST,
    ROLES_LIST,
    SERVER_SCOPES,
    TOKEN,
    TOKEN_RESPONSE,
    UPDATE_REALM_ROLES,
    USER_DETAILS,
)


@pytest.fixture
def mock_realm_role_data():
    mock_realm_role_data = schemas.RoleRequest(**UPDATE_REALM_ROLES)
    return mock_realm_role_data


@pytest.fixture
def mock_create_realm_role_data():
    mock_create_realm_role_data = schemas.RolePolicyRequest(**CREATE_REALM_ROLES)
    return mock_create_realm_role_data


@pytest.fixture
def mock_scopes():
    # mock_scopes = schemas.Role(**RESOURCE_RESPONSE)
    mock_scopes = schemas.ResourceResponse(**RESOURCE_RESPONSE)
    return mock_scopes


class TestKeycloakAuthIAM:
    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_keycloak_auth_iam_init(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value

        keycloak_auth_iam = KeycloakIAM(ka=mock_ka)

        # assert isinstance(keycloak_auth_iam.users_url, KeycloakUsersAPI)
        assert isinstance(keycloak_auth_iam.users, KeycloakUsersAPI)
        assert isinstance(keycloak_auth_iam.resource, KeycloakResourcesAPI)
        assert isinstance(
            keycloak_auth_iam.claim_permission_admin, KeycloakPermissionsAPI
        )
        assert isinstance(keycloak_auth_iam.groups, KeycloakGroupAPI)
        assert isinstance(keycloak_auth_iam.resources_admin, KeycloakResourcesAPI)
        assert isinstance(keycloak_auth_iam.clients, KeycloakClient)
        assert isinstance(keycloak_auth_iam.roles, KeycloakRolesAPI)

        # assert keycloak_auth_iam.users_url.ka == mock_ka
        assert keycloak_auth_iam.users.ka == mock_ka
        assert keycloak_auth_iam.resource.ka == mock_ka
        assert keycloak_auth_iam.claim_permission_admin.ka == mock_ka
        assert keycloak_auth_iam.groups.ka == mock_ka
        assert keycloak_auth_iam.resources_admin.ka == mock_ka
        assert keycloak_auth_iam.clients.ka == mock_ka
        assert keycloak_auth_iam.roles.ka == mock_ka


class TestKeycloakIAM:
    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_keycloak_auth_iam_init(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value

        keycloak_auth_iam = KeycloakIAM(ka=mock_ka)

        assert isinstance(keycloak_auth_iam.groups, KeycloakGroupAPI)
        assert keycloak_auth_iam.groups.ka == mock_ka


class TestKeycloakUsersAPI:
    @pytest.fixture
    def users_service(self, *args, **kwargs) -> AbstractUsersAPI:
        return InMemoryKeycloakUsersAPI()

    def test_get_evaluated_user_scope(self, users_service):
        result = users_service.get_evaluated_user_scope(TOKEN)
        assert result == TOKEN

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_evaluated_user_scope_success(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_user = mock_ka.users_url
        token = TOKEN
        actual_schema = user_schema.TokenResponse(access_token=token)

        instance = KeycloakUsersAPI(ka=mock_ka)

        mock_user.get_evaluated_user_scope.return_value = TOKEN_RESPONSE
        user_token = instance.get_evaluated_user_scope(token=token)

        assert user_token == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_users_success(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_user = mock_ka.users

        actual_schema = schemas.GroupMemberResponse(result=USER_DETAILS)

        instance = KeycloakUsersAPI(ka=mock_ka)
        mock_user.get_users.return_value = USER_DETAILS
        user_token = instance.get_users()
        assert user_token == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_users_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_user = mock_ka.users

        instance = KeycloakUsersAPI(ka=mock_ka)
        mock_user.get_users.return_value = USER_DETAILS
        mock_user.get_users.side_effect = UsersNotFound("No users found")

        with pytest.raises(UsersNotFound):
            instance.get_users()

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_users_not_found_list(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_user = mock_ka.users

        instance = KeycloakUsersAPI(ka=mock_ka)
        mock_user.get_users.return_value = []

        with pytest.raises(UsersNotFound):
            instance.get_users()

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_users_scopes_positive(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        instance = KeycloakUsersAPI(ka=mock_ka)

        resource_data = RESOURCE_LIST
        id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        actual_schema = [schemas.UserScope(**EVALUATE_SCOPE)]

        instance.users_scopes = MagicMock(return_value=resource_data)
        users_scopes_response = instance.get_users_scopes(id=id)
        assert users_scopes_response == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_users_scopes(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_user = mock_ka.users

        instance = KeycloakUsersAPI(ka=mock_ka)
        test_uuid = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        clientId = UUID4("7eab451e-4b45-4143-9c1a-ff60711e4add")

        mock_request_body = schemas.UserScopeRequest(
            clientId=clientId,
            userId=test_uuid,
            entitlements=False,
        )

        with patch(
            "app.adapters.iam.keycloak.jsonable_encoder", return_value=mock_request_body
        ):
            mock_evaluate_scopes_response = RESOURCE_LIST
            mock_user.evaluate_scopes.return_value = mock_evaluate_scopes_response

            result = instance.users_scopes(id=test_uuid)
            assert result == mock_evaluate_scopes_response


class TestKeycloakRolesAPI:
    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI.get_roles_list")
    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI.get_policy_list")
    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI.get_role_by_name")
    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI._create_policy_from_role_data")
    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_create_roles_success(
        self,
        MockKeycloak,
        mock_create_policy_from_role_data,
        mock_get_role_by_name,
        mock_get_policy_list,
        mock_get_roles_list,
        mock_create_realm_role_data,
    ):
        mock_ka = MockKeycloak.return_value
        mock_claim_permission = mock_ka.claim_permission_admin
        mock_realm_role = mock_ka.realm_roles
        instance = KeycloakRolesAPI(ka=mock_ka)

        policy_data_response = POLICY_RESPONSE
        policy_data = POLICIES_LIST

        mock_get_roles_list.return_value = schemas.RolesList(result=ROLES_LIST)
        mock_get_policy_list.return_value = schemas.PolicyList(result=policy_data)
        mock_get_role_by_name.return_value = schemas.RoleResponse(
            **REALM_ROLES_RESPONSE
        )
        mock_create_policy_from_role_data.return_value = schemas.PolicyResponse(
            **policy_data_response
        )

        permission_list = PERMISSION_LIST
        permission_list[0]["id"] = "37324656-e432-4395-a9ad-1cadfd5b8a9f"
        mock_claim_permission.get_many.return_value = permission_list
        mock_claim_permission.get.return_value = PERMISSION_DATA
        mock_realm_role.get_policies.return_value = []

        response = instance.create_role_policy(role=mock_create_realm_role_data)
        assert response.name == "Test"

    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI.get_roles_list")
    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI.get_policy_list")
    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI.get_role_by_name")
    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI._create_policy_from_role_data")
    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_create_roles_role_name_not_in_policy_name_list(
        self,
        MockKeycloak,
        mock_create_policy_from_role_data,
        mock_get_role_by_name,
        mock_get_policy_list,
        mock_get_roles_list,
        mock_create_realm_role_data,
    ):
        mock_ka = MockKeycloak.return_value
        mock_claim_permission = mock_ka.claim_permission_admin
        instance = KeycloakRolesAPI(ka=mock_ka)

        policy_data_response = POLICY_RESPONSE
        policy_details = POLICIES_LIST

        role_data = ROLES_LIST
        role_details = REALM_ROLES_RESPONSE
        role_details["name"] = "Test"
        role_details["description"] = "Test"
        role_data.append(role_details)

        mock_get_roles_list.return_value = schemas.RolesList(result=role_data)
        mock_get_policy_list.return_value = schemas.PolicyList(result=policy_details)
        mock_get_role_by_name.return_value = schemas.RoleResponse(
            **REALM_ROLES_RESPONSE
        )
        mock_create_policy_from_role_data.return_value = schemas.PolicyResponse(
            **policy_data_response
        )

        permission_list = PERMISSION_LIST
        permission_list[0]["id"] = "37324656-e432-4395-a9ad-1cadfd5b8a9f"
        mock_claim_permission.get_many.return_value = permission_list

        response = instance.create_role_policy(role=mock_create_realm_role_data)
        assert response.name == "Test"

    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI.get_roles_list")
    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI.get_policy_list")
    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI.get_role_by_name")
    @patch("app.adapters.iam.keycloak.KeycloakRolesAPI._create_policy_from_role_data")
    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_create_roles_role_name_already_exist_in_roles_policy_name_list(
        self,
        MockKeycloak,
        mock_create_policy_from_role_data,
        mock_get_role_by_name,
        mock_get_policy_list,
        mock_get_roles_list,
        mock_create_realm_role_data,
    ):
        mock_ka = MockKeycloak.return_value
        mock_claim_permission = mock_ka.claim_permission_admin
        instance = KeycloakRolesAPI(ka=mock_ka)

        policy_data_response = POLICY_RESPONSE
        policy_data = POLICIES_LIST
        policy_details = POLICY_DETAIL_RESPONSE
        policy_details["name"] = "Test"
        policy_details["description"] = "Test"
        policy_data.append(policy_details)

        role_data = ROLES_LIST
        role_details = REALM_ROLES_RESPONSE
        role_details["name"] = "Test"
        role_details["description"] = "Test"
        role_data.append(role_details)

        mock_get_roles_list.return_value = schemas.RolesList(result=role_data)
        mock_get_policy_list.return_value = schemas.PolicyList(result=policy_data)
        mock_get_role_by_name.return_value = schemas.RoleResponse(
            **REALM_ROLES_RESPONSE
        )
        mock_create_policy_from_role_data.return_value = schemas.PolicyResponse(
            **policy_data_response
        )

        permission_list = PERMISSION_LIST
        permission_list[0]["id"] = "37324656-e432-4395-a9ad-1cadfd5b8a9f"
        mock_claim_permission.get_many.return_value = permission_list

        with pytest.raises(AlreadyExist):
            instance.create_role_policy(role=mock_create_realm_role_data)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_policy_list_positive(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_policy = mock_ka.policy

        policy_data = POLICIES_LIST

        instance = KeycloakRolesAPI(ka=mock_ka)

        mock_policy.get_many.return_value = policy_data

        policy_list = instance.get_policy_list()

        actual_schema = schemas.PolicyList(result=policy_data)
        assert policy_list.result == actual_schema.result

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_policy_list_error(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_policy = mock_ka.policy

        policy_data = []

        instance = KeycloakRolesAPI(ka=mock_ka)

        mock_policy.get_many.return_value = policy_data

        with pytest.raises(PolicyNotFound):
            instance.get_policy_list()

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_role_by_id_positive(self, MockKeycloak):

        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles

        role_data = REALM_ROLES_RESPONSE
        instance = KeycloakRolesAPI(ka=mock_ka)

        mock_roles.get.return_value = role_data

        role_id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        role_response = instance.get_role_by_id(role_id=role_id)

        actual_schema = schemas.RoleResponse(**role_data)
        assert role_response == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_role_by_id_roles_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles

        mock_roles.get.side_effect = RoleNotFound("Role not found with Id: SomeId")

        instance = KeycloakRolesAPI(ka=mock_ka)

        with pytest.raises(RoleNotFound):
            role_id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
            instance.get_role_by_id(role_id=role_id)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_create_policy(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_policy = mock_ka.policy

        policy_data = POLICY
        policy_data_response = POLICY_RESPONSE

        instance = KeycloakRolesAPI(ka=mock_ka)

        mock_policy.create.return_value = policy_data_response

        create_policy_response = instance._create_policy(policy=policy_data)

        actual_schema = schemas.PolicyResponse(**policy_data_response)
        assert create_policy_response == actual_schema
        assert isinstance(create_policy_response, schemas.PolicyResponse)
        assert create_policy_response.id == "e6865c9c-a267-44be-9187-40c12ca77141"
        assert create_policy_response.name == "TestPolicy"
        assert create_policy_response.policyType == "role"

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_create_policy_policy_already_exist(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_policy = mock_ka.policy

        policy_data = POLICY
        policy_data_response = POLICY_RESPONSE

        instance = KeycloakRolesAPI(ka=mock_ka)

        mock_policy_response = MagicMock()
        mock_policy_response.json.return_value = policy_data_response
        mock_policy.create.return_value = mock_policy_response
        mock_policy.create.side_effect = PolicyAlreadyExist(
            "Role based Policy Already Exist."
        )

        with pytest.raises(PolicyAlreadyExist):
            instance._create_policy(policy=policy_data)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_roles_list_positive(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles
        instance = KeycloakRolesAPI(ka=mock_ka)

        roles_data = ROLES_LIST
        mock_roles.get_many.return_value = roles_data
        mock_roles.get_users_by_role_name.return_value = ROLE_WISE_USER_LIST
        # When page & page_size is None.
        roles_list = instance.get_roles_list()

        actual_schema = schemas.RolesList(result=ROLES_LIST)
        assert roles_list.result == actual_schema.result

        # When page & page_size is not None.
        page = 0
        page_size = 10
        roles_list = instance.get_roles_list(page=page, page_size=page_size)

        actual_schema = schemas.RolesList(result=ROLES_LIST)
        assert roles_list.result == actual_schema.result

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_roles_list_error(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles
        instance = KeycloakRolesAPI(ka=mock_ka)

        roles_data = []
        mock_roles.get_many.return_value = roles_data

        with pytest.raises(RoleNotFound):
            instance.get_roles_list()

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_role_by_name_positive(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles
        instance = KeycloakRolesAPI(ka=mock_ka)

        roles_data = REALM_ROLES_RESPONSE
        role_name = "Test"
        mock_roles.get_by_name.return_value = roles_data

        roles_response = instance.get_role_by_name(role_name=role_name)

        actual_schema = schemas.RoleResponse(**roles_data)
        assert roles_response.name == actual_schema.name
        assert roles_response.name == role_name

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_role_by_name_error(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles
        instance = KeycloakRolesAPI(ka=mock_ka)

        roles_data = REALM_ROLES_RESPONSE
        role_name = "Test"
        mock_roles.get_by_name.return_value = roles_data
        mock_roles.get_by_name.side_effect = RoleNotFound(
            f"Role with name '{role_name}' not found"
        )

        with pytest.raises(RoleNotFound):
            instance.get_role_by_name(role_name=role_name)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_create_policy_from_role_data(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        instance = KeycloakRolesAPI(ka=mock_ka)

        roles_data = REALM_ROLES_RESPONSE
        policy_data_response = POLICY_RESPONSE

        mock_role_response = schemas.RoleResponse(**roles_data)

        mock_policy_response = schemas.PolicyResponse(**policy_data_response)
        instance._create_policy = MagicMock(return_value=mock_policy_response)

        result = instance._create_policy_from_role_data(mock_role_response)

        assert isinstance(result, schemas.PolicyResponse)
        assert result.id == "e6865c9c-a267-44be-9187-40c12ca77141"
        assert result.name == "TestPolicy"
        assert result.description == "TestPolicy"
        assert len(result.roles) == 1
        assert result.roles[0].id == UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6")

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_delete_role_by_id_positive(self, MockKeycloak):

        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles

        instance = KeycloakRolesAPI(ka=mock_ka)
        role_data = REALM_ROLES_RESPONSE
        mock_roles.get.return_value = role_data

        mock_roles.delete.return_value = True

        role_id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        role_response = instance.delete_role_by_id(role_id=role_id)

        actual_response = True
        assert role_response == actual_response

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_delete_role_by_id_roles_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles

        role_id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        mock_roles.delete.side_effect = RoleNotFound(
            f"Role not found while deleting with Id: {str(role_id)}"
        )
        role_data = REALM_ROLES_RESPONSE
        mock_roles.get.return_value = role_data

        instance = KeycloakRolesAPI(ka=mock_ka)

        with pytest.raises(RoleNotFound):
            instance.delete_role_by_id(role_id=role_id)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_delete_role_by_id_default_role_error(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles

        role_data = REALM_ROLES_RESPONSE
        mock_roles.get.return_value = role_data

        role_id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        mock_roles.delete.side_effect = DefaultRoleError(
            f"For default role '{role_data['name']}' operation not allowed."
        )

        instance = KeycloakRolesAPI(ka=mock_ka)

        with pytest.raises(DefaultRoleError):
            instance.delete_role_by_id(role_id=role_id)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_delete_role_by_id_filter_default_role_error(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles

        role_data = REALM_ROLES_RESPONSE
        role_data["name"] = "DistributorUser"
        mock_roles.get.return_value = role_data

        role_id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        mock_roles.delete.side_effect = DefaultRoleError(
            f"For default role '{role_data['name']}' operation not allowed."
        )

        instance = KeycloakRolesAPI(ka=mock_ka)

        with pytest.raises(DefaultRoleError):
            instance.delete_role_by_id(role_id=role_id)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_update_role_positive(self, MockKeycloak, mock_realm_role_data):

        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles
        mock_permission = mock_ka.claim_permission_admin

        instance = KeycloakRolesAPI(ka=mock_ka)
        role_data = REALM_ROLES_RESPONSE
        role_data["name"] = "Test"
        mock_roles.get.return_value = role_data

        mock_roles.delete.return_value = True
        instance.get_policy_list = MagicMock(
            return_value=schemas.PolicyList(result=POLICIES_LIST)
        )
        instance._filter_role_data = MagicMock(return_value=True)
        mock_permission.get.return_value = PERMISSION_DATA
        mock_roles.get_policies.return_value = POLICIES_LIST

        role_id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        role = mock_realm_role_data
        role.name = "Test"
        scopes = schemas.ResourceResponse(**RESOURCE_RESPONSE)

        role_response = instance.update_role(
            role_id=role_id, role=role, scopes=[scopes]
        )
        actual_response = True
        assert role_response == actual_response

    @pytest.mark.parametrize(
        "role_name, expected_error_message",
        [
            (
                "DistributorAdmin",
                "For default role 'DistributorAdmin' operation not allowed.",
            ),
            (
                "DistributorUser",
                "For default role 'DistributorUser' operation not allowed.",
            ),
            ("ClientAdmin", "For default role 'ClientAdmin' operation not allowed."),
            ("ClientUser", "For default role 'ClientUser' operation not allowed."),
        ],
    )
    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_update_role_default_role_error(
        self, MockKeycloak, mock_realm_role_data, role_name, expected_error_message
    ):

        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles
        mock_permission = mock_ka.claim_permission_admin

        instance = KeycloakRolesAPI(ka=mock_ka)
        role_data = REALM_ROLES_RESPONSE
        role_data["name"] = role_name
        mock_roles.get.return_value = role_data

        mock_roles.delete.return_value = True
        instance.get_policy_list = MagicMock(
            return_value=schemas.PolicyList(result=POLICIES_LIST)
        )
        instance._filter_role_data = MagicMock(return_value=True)
        mock_permission.get_by_id.return_value = PERMISSION_DATA
        mock_roles.get_policies.return_value = POLICIES_LIST

        role_id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        role = mock_realm_role_data
        role.name = "Test"
        scopes = schemas.ResourceResponse(**RESOURCE_RESPONSE)

        with pytest.raises(DefaultRoleError) as e:

            instance.update_role(role_id=role_id, role=role, scopes=[scopes])
        assert str(e.value) == expected_error_message

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_update_role_role_not_found_error(self, MockKeycloak, mock_realm_role_data):

        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles
        mock_permission = mock_ka.claim_permission_admin

        instance = KeycloakRolesAPI(ka=mock_ka)
        role_data = REALM_ROLES_RESPONSE
        role_data["name"] = "Test"
        mock_roles.get.return_value = role_data
        mock_roles.get.side_effect = RoleNotFound("Role Not Found")

        mock_roles.delete.return_value = True
        instance.get_policy_list = MagicMock(
            return_value=schemas.PolicyList(result=POLICIES_LIST)
        )
        instance._filter_role_data = MagicMock(return_value=True)
        mock_permission.get_by_id.return_value = PERMISSION_DATA
        mock_roles.get_policies.return_value = POLICIES_LIST

        role_id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        role = mock_realm_role_data
        role.name = "Test"
        scopes = schemas.ResourceResponse(**RESOURCE_RESPONSE)

        with pytest.raises(RoleNotFound):
            instance.update_role(role_id=role_id, role=role, scopes=[scopes])

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_update_role_policy_not_found_error(
        self, MockKeycloak, mock_realm_role_data
    ):

        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles
        mock_permission = mock_ka.claim_permission_admin

        instance = KeycloakRolesAPI(ka=mock_ka)
        role_data = REALM_ROLES_RESPONSE
        role_data["name"] = "Test"
        mock_roles.get.return_value = role_data

        mock_roles.delete.return_value = True
        instance.get_policy_list = MagicMock(return_value=schemas.PolicyList(result=[]))
        instance._filter_role_data = MagicMock(return_value=True)
        mock_permission.get_by_id.return_value = PERMISSION_DATA
        mock_roles.get_policies.return_value = POLICIES_LIST

        role_id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        role = mock_realm_role_data
        role.name = "Test"
        scopes = schemas.ResourceResponse(**RESOURCE_RESPONSE)

        with pytest.raises(PolicyNotFound):
            instance.update_role(role_id=role_id, role=role, scopes=[scopes])

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_update_role_permission_not_found_error(
        self, MockKeycloak, mock_realm_role_data
    ):

        mock_ka = MockKeycloak.return_value
        mock_roles = mock_ka.realm_roles
        mock_permission = mock_ka.claim_permission_admin

        instance = KeycloakRolesAPI(ka=mock_ka)
        role_data = REALM_ROLES_RESPONSE
        role_data["name"] = "Test"
        mock_roles.get.return_value = role_data

        mock_roles.delete.return_value = True
        instance.get_policy_list = MagicMock(
            return_value=schemas.PolicyList(result=POLICIES_LIST)
        )
        instance._filter_role_data = MagicMock(return_value=True)
        mock_permission.get_by_id.return_value = PERMISSION_DATA
        mock_roles.get_policies.return_value = POLICIES_LIST

        role_id = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        role = mock_realm_role_data
        role.name = "Test"

        with pytest.raises(PermissionNotFound):
            instance.update_role(role_id=role_id, role=role, scopes=[])


class TestKeycloakPermissionsAPI:
    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_create_permission_success(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_claim_permission = mock_ka.claim_permission_admin

        claim_permission_data = PERMISSION_SCOPE_RESPONSE

        instance = KeycloakPermissionsAPI(ka=mock_ka)

        mock_claim_permission.create.return_value = claim_permission_data
        claim_permission_admin = schemas.ClaimPermission(**claim_permission_data)

        claim_permission_response = instance.create_permission(
            claim_permission_admin=claim_permission_admin
        )
        assert claim_permission_response.name == claim_permission_admin.name

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_permission_by_policy_success(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_claim_permission = mock_ka.claim_permission_admin

        claim_permission_data = POLICY_PERMISSION
        policy_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")

        instance = KeycloakPermissionsAPI(ka=mock_ka)

        mock_claim_permission.get_permission_by_policy_id.return_value = (
            claim_permission_data
        )
        claim_permission_admin = schemas.PermissionDetails(result=claim_permission_data)

        claim_permission_response = instance.get_permission_by_policy(
            policy_id=policy_id
        )
        assert claim_permission_response.result == claim_permission_admin.result

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_permission_by_policy_permission_not_found_list(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_claim_permission = mock_ka.claim_permission_admin

        claim_permission_data = []
        policy_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")

        instance = KeycloakPermissionsAPI(ka=mock_ka)

        mock_claim_permission.get_permission_by_policy_id.return_value = (
            claim_permission_data
        )
        response = instance.get_permission_by_policy(policy_id=policy_id)
        assert response.result == []

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_permission_by_policy_policy_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_claim_permission = mock_ka.claim_permission_admin

        claim_permission_data = []
        policy_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")

        instance = KeycloakPermissionsAPI(ka=mock_ka)

        mock_claim_permission.get_permission_by_policy_id.return_value = (
            claim_permission_data
        )
        mock_claim_permission.get_permission_by_policy_id.side_effect = PolicyNotFound(
            f"Policy with ID: '{policy_id}' not found"
        )

        with pytest.raises(PolicyNotFound):
            instance.get_permission_by_policy(policy_id=policy_id)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_permission_by_policy_permission_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_claim_permission = mock_ka.claim_permission_admin

        claim_permission_data = []
        policy_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")

        instance = KeycloakPermissionsAPI(ka=mock_ka)

        mock_claim_permission.get_permission_by_policy_id.return_value = (
            claim_permission_data
        )
        mock_claim_permission.get_permission_by_policy_id.side_effect = (
            PermissionNotFound(f"Permission data not found for policy_id: {policy_id}")
        )

        with pytest.raises(PermissionNotFound):
            instance.get_permission_by_policy(policy_id=policy_id)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_permission_success(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_claim_permission = mock_ka.claim_permission_admin

        claim_permission_data = PERMISSION
        scope_id = UUID4("*************-4403-8550-c993ca0f57d0")

        instance = KeycloakPermissionsAPI(ka=mock_ka)

        mock_claim_permission.get_permission_by_scope_id.return_value = [
            claim_permission_data
        ]
        claim_permission_admin = schemas.PermissionDetails(
            result=[schemas.PermissionScope(**claim_permission_data)]
        )

        claim_permission_response = instance.get_permission(scope_id=scope_id)
        assert claim_permission_response == claim_permission_admin

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_permission_permission_not_found_list(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_claim_permission = mock_ka.claim_permission_admin

        scope_id = UUID4("*************-4403-8550-c993ca0f57d0")

        instance = KeycloakPermissionsAPI(ka=mock_ka)

        mock_claim_permission.get_permission_by_scope_id.return_value = []

        with pytest.raises(PermissionNotFound):
            instance.get_permission(scope_id=scope_id)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_permission_permission_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_claim_permission = mock_ka.claim_permission_admin

        claim_permission_data = PERMISSION
        scope_id = UUID4("*************-4403-8550-c993ca0f57d0")

        instance = KeycloakPermissionsAPI(ka=mock_ka)

        mock_claim_permission.get_permission_by_scope_id.return_value = [
            claim_permission_data
        ]
        mock_claim_permission.get_permission_by_scope_id.side_effect = (
            PermissionNotFound(
                "Permission data not found for scope_id: {}".format(scope_id)
            )
        )
        with pytest.raises(PermissionNotFound):
            instance.get_permission(scope_id=scope_id)


class TestKeycloakGroupAuthAPI:
    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_group_roles_positive(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_group_roles = mock_ka.groups

        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        role_data = ROLES_LIST

        instance = KeycloakGroupAPI(ka=mock_ka)

        # mock_group_roles_response = MagicMock()
        # mock_group_roles_response.json.return_value = role_data
        mock_group_roles.get_group_roles.return_value = role_data

        mock_group_roles.get_group_role_wise_user_list.return_value = (
            ROLE_WISE_USER_LIST
        )

        roles_list = instance.get_group_roles(group_id=group_id)

        actual_schema = schemas.RolesList(result=role_data)

        assert roles_list.result[0] == actual_schema.result[0]

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_group_roles_roles_not_found_list_empty(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_group_roles = mock_ka.groups

        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_group_roles.get_group_roles.return_value = []

        with pytest.raises(RoleNotFound):
            instance.get_group_roles(group_id=group_id)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_group_roles_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_group_roles = mock_ka.groups

        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        role_data = ROLES_LIST

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_group_roles.get_group_roles.return_value = role_data
        mock_group_roles.get_group_roles.side_effect = RoleNotFound(
            "Group roles not found."
        )

        with pytest.raises(RoleNotFound):
            instance.get_group_roles(group_id=group_id)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_assign_group_roles_positive(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_groups_auth = mock_ka.groups
        mock_roles = mock_ka.realm_roles

        role_data = ROLES_LIST
        group_roles = schemas.AssignGroupRoles(roles=ASSIGN_GROUP_ROLES)

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_roles.get_many.return_value = GROUP_ROLES_LIST

        mock_groups_auth.assign_group_roles.return_value = schemas.RolesList(
            result=ROLES_LIST
        )

        mock_get_group_roles = MagicMock()
        expected_group_roles_response = schemas.RolesList(result=ROLES_LIST)
        mock_get_group_roles.return_value = expected_group_roles_response
        instance.get_group_roles = mock_get_group_roles

        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        assign_group_roles_response = instance.assign_group_roles(
            group_id=group_id, group_roles=group_roles
        )

        actual_schema = schemas.RolesList(result=role_data)
        assert assign_group_roles_response == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_assign_group_roles_roles_not_found_not_role_data(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_groups_auth = mock_ka.groups
        mock_roles = mock_ka.realm_roles

        group_roles = schemas.AssignGroupRoles(roles=ASSIGN_GROUP_ROLES)

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_roles.get_many.return_value = []

        mock_groups_auth.assign_group_roles.return_value = schemas.RolesList(
            result=ROLES_LIST
        )

        mock_get_group_roles = MagicMock()
        expected_group_roles_response = schemas.RolesList(result=ROLES_LIST)
        mock_get_group_roles.return_value = expected_group_roles_response
        instance.get_group_roles = mock_get_group_roles

        with pytest.raises(RoleNotFound):
            group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
            instance.assign_group_roles(group_id=group_id, group_roles=group_roles)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_assign_group_roles_roles_not_found_list_empty(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_groups_auth = mock_ka.groups
        mock_roles = mock_ka.realm_roles

        group_roles = schemas.AssignGroupRoles(
            roles=[schemas.GroupRoles(id="a88c8a9a-082d-4fa7-af11-62ede6de7ebc")]
        )

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_roles.get_many.return_value = GROUP_ROLES_LIST

        mock_groups_auth.assign_group_roles.return_value = schemas.RolesList(
            result=ROLES_LIST
        )

        mock_get_group_roles = MagicMock()
        expected_group_roles_response = schemas.RolesList(result=ROLES_LIST)
        mock_get_group_roles.return_value = expected_group_roles_response
        instance.get_group_roles = mock_get_group_roles

        with pytest.raises(RoleNotFound):
            group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
            instance.assign_group_roles(group_id=group_id, group_roles=group_roles)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_assign_group_roles_group_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_groups_auth = mock_ka.groups
        mock_roles = mock_ka.realm_roles

        group_roles = schemas.AssignGroupRoles(roles=ASSIGN_GROUP_ROLES)

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_roles.get_many.return_value = GROUP_ROLES_LIST

        mock_groups_auth.assign_group_roles.return_value = schemas.RolesList(
            result=ROLES_LIST
        )
        mock_groups_auth.assign_group_roles.side_effect = GroupNotFound(
            "Group not found"
        )

        mock_get_group_roles = MagicMock()
        expected_group_roles_response = schemas.RolesList(result=ROLES_LIST)
        mock_get_group_roles.return_value = expected_group_roles_response
        instance.get_group_roles = mock_get_group_roles

        with pytest.raises(GroupNotFound):
            group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
            instance.assign_group_roles(group_id=group_id, group_roles=group_roles)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_assign_groupss_roles_group_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_groups_auth = mock_ka.groups
        mock_roles = mock_ka.realm_roles

        group_roles = schemas.AssignGroupRoles(roles=ASSIGN_GROUP_ROLES)

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_roles.get_many.return_value = GROUP_ROLES_LIST
        mock_roles.get_many.side_effect = RoleNotFound("Roles not found")

        mock_groups_auth.assign_group_roles.return_value = schemas.RolesList(
            result=ROLES_LIST
        )
        mock_groups_auth.assign_group_roles.side_effect = GroupNotFound(
            "Group not found"
        )

        mock_get_group_roles = MagicMock()
        expected_group_roles_response = schemas.RolesList(result=ROLES_LIST)
        mock_get_group_roles.return_value = expected_group_roles_response
        instance.get_group_roles = mock_get_group_roles

        with pytest.raises(RoleNotFound):
            group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
            instance.assign_group_roles(group_id=group_id, group_roles=group_roles)


class TestKeycloakGroupAPI:
    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_group_members_positive(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value

        mock_groups = MagicMock()
        mock_ka.groups = mock_groups

        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        page = 0
        page_size = 0
        group_members_data = USER_DETAILS

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_groups.get_members.return_value = group_members_data

        group_members_list = instance.get_group_members(
            group_id=group_id, page=page, page_size=page_size
        )

        actual_schema = schemas.GroupMemberResponse(result=USER_DETAILS)
        assert group_members_list.result == actual_schema.result

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_group_members_members_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value

        mock_groups = MagicMock()
        mock_ka.groups = mock_groups

        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        page = 0
        page_size = 0
        group_members_data = USER_DETAILS

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_groups.get_members.return_value = group_members_data
        mock_groups.get_members.side_effect = MemberNotFound("Group members not found.")

        with pytest.raises(MemberNotFound):
            instance.get_group_members(
                group_id=group_id, page=page, page_size=page_size
            )

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_group_members_members_not_found_list(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value

        mock_groups = MagicMock()
        mock_ka.groups = mock_groups

        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        page = 0
        page_size = 0
        group_members_data = []

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_groups.get_members.return_value = group_members_data

        with pytest.raises(MemberNotFound):
            instance.get_group_members(
                group_id=group_id, page=page, page_size=page_size
            )

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_group_by_parent_path_positive(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value

        mock_groups = MagicMock()
        mock_ka.groups = mock_groups

        group_path = "Nextgen Clearing"
        group_data = GROUP_SUB_GROUP
        group_id = "9eab741e-4b45-4143-9c1a-ff60782e4add"
        instance = KeycloakGroupAPI(ka=mock_ka)

        group_data["name"] = group_path
        group_data["path"] = "/" + group_path
        mock_groups.get_subgroup.return_value = [group_data]

        groups_response = instance.get_subgroup_by_group(group_id=group_id)

        assert groups_response[0].name == "Nextgen Clearing"
        assert groups_response[0].path == "/Nextgen Clearing"

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_group_by_parent_path_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value

        mock_groups = MagicMock()
        mock_ka.groups = mock_groups

        group_path = "Nextgen Clearings"
        group_data = GROUP_SUB_GROUP
        group_id = "9eab741e-4b45-4143-9c1a-ff60782e4add"

        instance = KeycloakGroupAPI(ka=mock_ka)

        group_data["name"] = group_path
        group_data["path"] = "/" + group_path
        mock_groups.get_subgroup.return_value = [group_data]
        mock_groups.get_subgroup.side_effect = GroupNotFound("Group details not found.")

        with pytest.raises(GroupNotFound):
            instance.get_subgroup_by_group(group_id=group_id)

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_groups_list_positive(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value

        mock_groups = MagicMock()
        mock_ka.groups = mock_groups

        group_data = GROUPS

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_groups.get_many.return_value = group_data

        groups_response = instance.groups_list()

        actual_schema = schemas.GroupsResponse(result=GROUPS)
        assert groups_response != actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_groups_list_not_found(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value

        mock_groups = MagicMock()
        mock_ka.groups = mock_groups

        group_data = GROUPS

        instance = KeycloakGroupAPI(ka=mock_ka)

        mock_groups.get_many.return_value = group_data
        mock_groups.get_many.side_effect = GroupNotFound("Group details not found.")

        with pytest.raises(GroupNotFound):
            instance.groups_list()


class TestKeycloakResourcesAPI:
    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_create_resources_success(self, MockKeycloak):
        self.test_get_resource_success()
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources

        actual_schema = schemas.ResourceResponse(**RESOURCE_RESPONSE)

        instance = KeycloakResourcesAPI(ka=mock_ka)
        mock_resource.create.return_value = RESOURCE_RESPONSE
        resource_response = instance.create_resources(
            resource=schemas.Resource(**CREATE_RESOURCE)
        )
        assert resource_response == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_update_resource(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        instance = KeycloakResourcesAPI(ka=mock_ka)

        instance._get_server_scopes = MagicMock(return_value=SERVER_SCOPES)
        instance._merge_scopes = MagicMock(return_value=MERGED_SCOPES)
        instance._create_payload = MagicMock(return_value=PAYLOAD_DATA)
        instance._update_resource_payload = MagicMock(return_value=None)

        resource_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        scope_id = "547642d4-0bac-4c14-94a0-a8e507c8e41c"
        resource = schemas.ScopeList(resource_scopes=[scope_id])
        response = instance.update_resource(resource_id, resource)
        expected_response = None

        assert response == expected_response

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_server_scopes(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources
        instance = KeycloakResourcesAPI(ka=mock_ka)

        mock_resource.get.return_value = RESOURCE_RESPONSE

        resource_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        server_scopes = instance._get_server_scopes(resource_id)
        # assert server_scopes == SERVER_SCOPES
        assert server_scopes == ["account_create/modify"]

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_merge_scopes(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        instance = KeycloakResourcesAPI(ka=mock_ka)

        # When both user_scopes and server_scopes are empty
        user_scopes = []
        server_scopes = []
        merged_scopes = instance._merge_scopes(user_scopes, server_scopes)
        assert merged_scopes == []

        # When user_scopes is empty
        user_scopes = []
        server_scopes = SERVER_SCOPES
        merged_scopes = instance._merge_scopes(user_scopes, server_scopes)
        assert merged_scopes == SERVER_SCOPES

        # When server_scopes is empty
        user_scopes = ["547642d4-0bac-4c14-94a0-a8e507c8e41c"]
        server_scopes = []
        merged_scopes = instance._merge_scopes(user_scopes, server_scopes)
        assert merged_scopes == user_scopes

        # When both user_scopes and server_scopes have values
        user_scopes = "547642d4-0bac-4c14-94a0-a8e507c8e41c"
        server_scopes = SERVER_SCOPES
        merged_scopes = instance._merge_scopes([user_scopes], server_scopes)
        assert user_scopes in merged_scopes

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_create_payload(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        instance = KeycloakResourcesAPI(ka=mock_ka)

        instance._get_scopes_by_resource = MagicMock(
            return_value=schemas.UpdateResource(**RESOURCE_RESPONSE)
        )

        resource_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        merged_scopes = MERGED_SCOPES

        payload_data = instance._create_payload(resource_id, merged_scopes)
        assert payload_data == PAYLOAD_DATA

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_scopes_by_resource(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources

        instance = KeycloakResourcesAPI(ka=mock_ka)
        actual_schema = schemas.UpdateResource(**RESOURCE_RESPONSE)

        mock_resource.get.return_value = RESOURCE_RESPONSE

        resource_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        resource = instance._get_scopes_by_resource(resource_id)

        assert resource == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_update_resource_payload(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources

        instance = KeycloakResourcesAPI(ka=mock_ka)
        mock_resource.update.return_value = None

        resource_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        payload_data = PAYLOAD_DATA

        response = instance._update_resource_payload(resource_id, payload_data)
        expected_response = None
        assert response == expected_response

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_resource_success(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources_admin
        instance = KeycloakResourcesAPI(ka=mock_ka)

        resource = RESOURCE_RESPONSE

        mock_resource.get_resources_by_client_id.return_value = [resource]
        actual_schema = [schemas.ResourceResponse(**resource)]

        response = instance.get_resource()
        assert response == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_resource_by_id_payload(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources_admin
        instance = KeycloakResourcesAPI(ka=mock_ka)

        resource = RESOURCE_RESPONSE

        mock_resource.get_resource_by_id.return_value = resource
        actual_schema = schemas.ResourceResponse(**resource)

        resource_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        response = instance.get_resource_by_id(resource_id=resource_id)
        assert response == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_resource_scopes_and_permissions_success(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        instance = KeycloakResourcesAPI(ka=mock_ka)

        resource = RESOURCE_RESPONSE
        common_response = PERMISSION_BY_SCOPE_COMMON_RESPONSE
        permission_scope = schemas.ScopeSchema(**common_response)

        instance.get_resource = MagicMock(
            return_value=[schemas.ResourceResponse(**resource)]
        )
        instance._get_permissions_by_scope = MagicMock(return_value=[permission_scope])

        actual_schema = [schemas.ResourceResponse(**resource)]

        response = instance.get_resource_scopes_and_permissions()
        assert response == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_permissions_by_scope_success(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        instance = KeycloakResourcesAPI(ka=mock_ka)

        permission = PERMISSION
        common_response = PERMISSION_BY_SCOPE_COMMON_RESPONSE
        permission_schema = schemas.ScopeSchema(**common_response)

        instance._get_permissions = MagicMock(
            return_value=[schemas.PermissionScope(**permission)]
        )

        response = instance._get_permissions_by_scope(scopes=[permission_schema])

        assert response == [permission_schema]

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_permissions_success(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources_admin
        instance = KeycloakResourcesAPI(ka=mock_ka)

        permission = PERMISSION

        mock_resource.get_permissions_by_scope_id.return_value = [permission]
        actual_schema = [schemas.PermissionScope(**permission)]

        scope_id = UUID4("*************-4403-8550-c993ca0f57d0")
        response = instance._get_permissions(scope_id=scope_id)
        assert response == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_get_policies_by_permission_success(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources_admin
        instance = KeycloakResourcesAPI(ka=mock_ka)

        policy_data = POLICY_DETAIL_RESPONSE

        mock_resource.get_policy_by_permission.return_value = [policy_data]
        actual_schema = [schemas.PolicyDetailsResponse(**policy_data)]

        permission_id = UUID4("37324656-e432-4395-a9ad-1cadfd5b8a9f")
        response = instance.get_policies_by_permission(permission_id=permission_id)
        assert response == actual_schema

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_delete_resource_by_id(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources
        mock_scope = mock_ka.scopes_admin
        instance = KeycloakResourcesAPI(ka=mock_ka)

        resource_id = UUID4("be52a98c-8b47-4c6b-b586-29d68d875a63")
        resource = RESOURCE_RESPONSE

        instance.get_resource_by_id = MagicMock(
            return_value=schemas.ResourceResponse(**resource)
        )

        mock_resource.delete.return_value = True
        mock_scope.delete.return_value = True

        actual_response = True

        response = instance.delete_resource_by_id(resource_id=resource_id)
        assert response == actual_response

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_delete_resource_by_id_error_get_resource_by_id(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources
        mock_scope = mock_ka.scopes_admin
        instance = KeycloakResourcesAPI(ka=mock_ka)

        resource_id = UUID4("be52a98c-8b47-4c6b-b586-29d68d875a63")
        resource = RESOURCE_RESPONSE
        error_response = ResourceNotFound(
            f"Resource not found while getting with Id: {str(resource_id)}"
        )

        instance.get_resource_by_id = MagicMock(
            return_value=schemas.ResourceResponse(**resource)
        )
        instance.get_resource_by_id = MagicMock(side_effect=error_response)

        mock_resource.delete.return_value = True
        mock_scope.delete.return_value = True

        with pytest.raises(ResourceNotFound) as exc_info:
            instance.delete_resource_by_id(resource_id=resource_id)

        assert exc_info.value == error_response

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_delete_resource_by_id_error_delete_resource(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources
        mock_scope = mock_ka.scopes_admin
        instance = KeycloakResourcesAPI(ka=mock_ka)

        resource_id = UUID4("be52a98c-8b47-4c6b-b586-29d68d875a63")
        resource = RESOURCE_RESPONSE
        error_response = ResourceNotFound(
            f"Resource not found while deleting with Id: {str(resource_id)}"
        )

        instance.get_resource_by_id = MagicMock(
            return_value=schemas.ResourceResponse(**resource)
        )

        mock_resource.delete.return_value = True
        mock_resource.delete.side_effect = error_response
        mock_scope.delete.return_value = True

        with pytest.raises(ResourceNotFound) as exc_info:
            instance.delete_resource_by_id(resource_id=resource_id)

        assert exc_info.value == error_response

    @patch("app.adapters.iam.keycloak.KeycloakAdmin")
    def test_delete_resource_by_id_error_delete_scope(self, MockKeycloak):
        mock_ka = MockKeycloak.return_value
        mock_resource = mock_ka.resources
        mock_scope = mock_ka.scopes_admin
        instance = KeycloakResourcesAPI(ka=mock_ka)

        resource_id = UUID4("be52a98c-8b47-4c6b-b586-29d68d875a63")
        scope_id = UUID4("bee35715-1061-4186-906e-d7d6fa2b0b5a")
        resource = RESOURCE_RESPONSE
        error_response = ScopeNotFound(
            f"Scope not found while deleting with Id: {str(scope_id)}"
        )

        instance.get_resource_by_id = MagicMock(
            return_value=schemas.ResourceResponse(**resource)
        )

        mock_resource.delete.return_value = True
        mock_scope.delete.return_value = True
        mock_scope.delete.side_effect = error_response

        with pytest.raises(ScopeNotFound) as exc_info:
            instance.delete_resource_by_id(resource_id=resource_id)

        assert exc_info.value == error_response
