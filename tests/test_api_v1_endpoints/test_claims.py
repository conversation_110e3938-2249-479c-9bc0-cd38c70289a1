from unittest.mock import Magic<PERSON>ock

import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from pydantic import <PERSON>UID4

from app.adapters.iam import schemas
from app.adapters.iam.base import AbstractClaimPermissionAPI, AbstractIAM
from app.adapters.iam.exc import (
    PermissionNotFound,
    PolicyNotFound,
    ResourceNotFound,
    ScopeNotFound,
    UnauthorizedUser,
)
from app.api.api_v1.endpoints.claims import (
    get_permission_by_policy,
    get_permission_by_scope,
)
from app.api.api_v1.examples import ACTOR_DETAILS, POLICY_PERMISSION, SCOPE_PERMISSION
from app.schemas.auth import RealmAccess, UserInfo


@pytest.fixture
def mock_actor():
    roles = [
        "ClinetAdmin",
        "create-realm",
        "default-roles-master",
        "offline_access",
        "admin",
        "DistributorAdmin",
        "uma_authorization",
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_actor_error():
    roles = [
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_iam():
    mock_iam = MagicMock(spec=AbstractIAM)
    mock_iam.claim_permission_admin = MagicMock(spec=AbstractClaimPermissionAPI)
    mock_iam.claim_permission_admin.get_permission_by_policy.return_value = (
        schemas.PermissionDetails(result=POLICY_PERMISSION)
    )
    mock_iam.claim_permission_admin.get_permission.return_value = (
        schemas.PermissionScope(**SCOPE_PERMISSION)
    )
    return mock_iam


@pytest.fixture
def mock_iam_no_permission():
    mock_iam = MagicMock(spec=AbstractIAM)
    mock_iam.claim_permission_admin = MagicMock(spec=AbstractClaimPermissionAPI)
    mock_iam.claim_permission_admin.get_permission_by_policy.return_value = (
        schemas.PermissionDetails(result=[])
    )
    return mock_iam


@pytest.fixture
def mock_iam_error():
    def auth_factory(error_type, message):
        mock_iam = MagicMock(spec=AbstractIAM)
        mock_iam.claim_permission_admin = MagicMock(spec=AbstractClaimPermissionAPI)
        mock_iam.claim_permission_admin.get_permission_by_policy.return_value = (
            schemas.PermissionDetails(result=POLICY_PERMISSION)
        )
        mock_iam.claim_permission_admin.get_permission.return_value = (
            schemas.PermissionScope(**SCOPE_PERMISSION)
        )
        mock_iam.claim_permission_admin.get_permission_by_policy.side_effect = (
            error_type(message)
        )
        mock_iam.claim_permission_admin.get_permission.side_effect = error_type(message)
        return mock_iam

    return auth_factory


class TestClaims:
    def test_get_permission_by_policy_success(self, mock_actor, mock_iam):
        policy_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        response = get_permission_by_policy(
            policy_id=policy_id,
            iam=mock_iam,
            actor=mock_actor,
        )
        response_schema = schemas.PermissionDetails(result=POLICY_PERMISSION)
        assert response.result == response_schema.result

    def test_get_permission_by_policy_policy_not_found(
        self, mock_actor, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=PolicyNotFound,
            message="Policy not found.",
        )
        policy_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            get_permission_by_policy(
                policy_id=policy_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Policy not found." in exc_info.value.detail

    def test_get_permission_by_policy_permission_not_found(
        self, mock_actor, mock_iam_no_permission
    ):
        policy_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        response = get_permission_by_policy(
            policy_id=policy_id,
            iam=mock_iam_no_permission,
            actor=mock_actor,
        )
        response_schema = schemas.PermissionDetails(result=[])
        assert response.result == response_schema.result

    def test_get_permission_by_policy_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We Couldn't process your request.",
        )
        policy_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            get_permission_by_policy(
                policy_id=policy_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We Couldn't process your request." in exc_info.value.detail

    def test_get_permission_by_policy_unauthorized(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=UnauthorizedUser,
            message="Unauthorized access.",
        )
        policy_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            get_permission_by_policy(
                policy_id=policy_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Unauthorized access." in exc_info.value.detail

    def test_get_permission_by_policy_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request",
        )
        policy_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            get_permission_by_policy(
                policy_id=policy_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request" in exc_info.value.detail

    def test_get_permission_by_scope_success(self, mock_actor, mock_iam):
        scope_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        response = get_permission_by_scope(
            scope_id=scope_id,
            iam=mock_iam,
            actor=mock_actor,
        )
        response_schema = schemas.PermissionScope(**SCOPE_PERMISSION)
        assert response == response_schema

    def test_get_permission_by_scope_resource_not_found(
        self, mock_actor, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=ResourceNotFound,
            message="Resource not found.",
        )
        scope_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            get_permission_by_scope(
                scope_id=scope_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Resource not found." in exc_info.value.detail

    def test_get_permission_by_scope_permission_not_found(
        self, mock_actor, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=PermissionNotFound,
            message="Permission data not found.",
        )
        scope_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            get_permission_by_scope(
                scope_id=scope_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Permission data not found." in exc_info.value.detail

    def test_get_permission_by_scope_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We Couldn't process your request.",
        )
        scope_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            get_permission_by_scope(
                scope_id=scope_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We Couldn't process your request." in exc_info.value.detail

    def test_get_permission_by_scope_unauthorized(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=UnauthorizedUser,
            message="Unauthorized access.",
        )
        scope_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            get_permission_by_scope(
                scope_id=scope_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Unauthorized access." in exc_info.value.detail

    def test_get_permission_by_scope_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request",
        )
        scope_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            get_permission_by_scope(
                scope_id=scope_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request" in exc_info.value.detail

    def test_get_permission_by_scope_forbidden(self, mock_actor_error, mock_iam):
        scope_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            get_permission_by_scope(
                scope_id=scope_id,
                iam=mock_iam,
                actor=mock_actor_error,
            )
        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert "You are trying to access a restricted area." in exc_info.value.detail

    def test_get_permission_by_scope_scope_not_found(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ScopeNotFound,
            message="Scope not found.",
        )
        scope_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            get_permission_by_scope(
                scope_id=scope_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Scope not found." in exc_info.value.detail
