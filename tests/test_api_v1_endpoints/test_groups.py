from unittest.mock import MagicMock

import pytest
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>, status
from pydantic import UUID4

from app.adapters.iam import schemas
from app.adapters.iam.base import AbstractGroupAPI, AbstractIAM
from app.adapters.iam.exc import (
    GroupNotFound,
    MaxNumberInvalid,
    MemberNotFound,
    RoleNotFound,
    UnauthorizedUser,
)
from app.api.api_v1.endpoints.groups import (
    assign_group_roles,
    get_group_and_subgroup,
    group_members,
    group_roles,
    subgroup_by_group,
)
from app.api.api_v1.examples import (
    ACTOR_DETAILS,
    ASSIGN_GROUP_ROLES,
    GROUPS,
    ROLES_LIST,
    USER_DETAILS,
)
from app.schemas.auth import RealmAccess, UserInfo


@pytest.fixture
def mock_actor():
    roles = [
        "ClinetAdmin",
        "create-realm",
        "default-roles-master",
        "offline_access",
        "admin",
        "DistributorAdmin",
        "uma_authorization",
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_actor_error():
    roles = [
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_actor_without_role():
    roles = []
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_iam():
    mock_iam = MagicMock(spec=AbstractIAM)
    mock_iam.groups = MagicMock(spec=AbstractGroupAPI)
    mock_iam.groups.get_group_roles.return_value = schemas.RolesList(result=ROLES_LIST)
    mock_iam.groups.assign_group_roles.return_value = schemas.RolesList(
        result=ROLES_LIST
    )

    mock_iam.groups.get_group_members.return_value = schemas.GroupMemberResponse(
        result=USER_DETAILS
    )
    mock_iam.groups.groups_list.return_value = schemas.GroupsResponse(result=GROUPS)
    return mock_iam


@pytest.fixture
def mock_iam_error():
    def auth_factory(error_type, message):
        mock_iam = MagicMock(spec=AbstractIAM)
        mock_iam.groups = MagicMock(spec=AbstractGroupAPI)
        mock_iam.groups.get_group_roles.return_value = schemas.RolesList(
            result=ROLES_LIST
        )
        mock_iam.groups.assign_group_roles.return_value = schemas.RolesList(
            result=ROLES_LIST
        )
        mock_iam.groups.get_group_members.return_value = schemas.GroupMemberResponse(
            result=USER_DETAILS
        )
        mock_iam.groups.groups_list.return_value = schemas.GroupsResponse(result=GROUPS)

        mock_iam.groups.get_group_members.side_effect = error_type(message)
        mock_iam.groups.groups_list.side_effect = error_type(message)
        mock_iam.groups.get_group_roles.side_effect = error_type(message)
        mock_iam.groups.assign_group_roles.side_effect = error_type(message)
        return mock_iam

    return auth_factory


class TestGroups:
    def test_get_group_roles_success(self, mock_actor, mock_iam):
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        response = group_roles(
            group_id=group_id,
            iam=mock_iam,
            actor=mock_actor,
        )
        response_schema = schemas.RolesList(result=ROLES_LIST)
        assert response.result == response_schema.result

    def test_get_group_roles_not_found(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=RoleNotFound,
            message="Group roles not found.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            group_roles(
                group_id=group_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Group roles not found." in exc_info.value.detail

    def test_get_group_roles_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We couldn't process your request.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            group_roles(
                group_id=group_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process your request." in exc_info.value.detail

    def test_get_group_roles_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        with pytest.raises(HTTPException) as exc_info:
            group_roles(
                group_id=group_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail

    def test_group_members_success(self, mock_actor, mock_iam):
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        page = 0
        page_size = 0
        response = group_members(
            group_id=group_id,
            page=page,
            page_size=page_size,
            iam=mock_iam,
            actor=mock_actor,
        )
        response_schema = schemas.GroupMemberResponse(result=USER_DETAILS)
        assert response.result == response_schema.result

    def test_group_members_not_found(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=MemberNotFound,
            message="Group members not found.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        page = 0
        page_size = 0
        with pytest.raises(HTTPException) as exc_info:
            group_members(
                group_id=group_id,
                page=page,
                page_size=page_size,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Group members not found." in exc_info.value.detail

    def test_group_members_group_not_found(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=GroupNotFound,
            message="Group details not found.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        page = 0
        page_size = 0
        with pytest.raises(HTTPException) as exc_info:
            group_members(
                group_id=group_id,
                page=page,
                page_size=page_size,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Group details not found." in exc_info.value.detail

    def test_group_members_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We couldn't process your request.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        page = 0
        page_size = 0
        with pytest.raises(HTTPException) as exc_info:
            group_members(
                group_id=group_id,
                page=page,
                page_size=page_size,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process your request." in exc_info.value.detail

    def test_group_members_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        page = 0
        page_size = 0
        with pytest.raises(HTTPException) as exc_info:
            group_members(
                group_id=group_id,
                page=page,
                page_size=page_size,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail

    def test_group_members_unauthorised(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=UnauthorizedUser,
            message="Unauthorized access.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        page = 0
        page_size = 0
        with pytest.raises(HTTPException) as exc_info:
            group_members(
                group_id=group_id,
                page=page,
                page_size=page_size,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Unauthorized access." in exc_info.value.detail

    def test_group_members_forbidden(self, mock_actor_error, mock_iam):
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        page = 0
        page_size = 0
        with pytest.raises(HTTPException) as exc_info:
            group_members(
                group_id=group_id,
                page=page,
                page_size=page_size,
                iam=mock_iam,
                actor=mock_actor_error,
            )
        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert "You are trying to access a restricted area." in exc_info.value.detail

    def test_group_members_max_number_invalid(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=MaxNumberInvalid,
            message="max Number should be a multiple of 10",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        page = 0
        page_size = 0
        with pytest.raises(HTTPException) as exc_info:
            group_members(
                group_id=group_id,
                page=page,
                page_size=page_size,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "max Number should be a multiple of 10" in exc_info.value.detail

    def test_assign_group_roles_success(self, mock_actor, mock_iam):
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        group_roles = schemas.AssignGroupRoles(roles=ASSIGN_GROUP_ROLES)
        response = assign_group_roles(
            group_id=group_id,
            group_roles=group_roles,
            iam=mock_iam,
            actor=mock_actor,
        )
        response_schema = schemas.RolesList(result=ROLES_LIST)
        assert response.result == response_schema.result

    def test_assign_group_roles_roles_not_found(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=RoleNotFound,
            message="Role not found.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        group_roles = schemas.AssignGroupRoles(roles=ASSIGN_GROUP_ROLES)
        with pytest.raises(HTTPException) as exc_info:
            assign_group_roles(
                group_id=group_id,
                group_roles=group_roles,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Roles requested for assignment not found." in exc_info.value.detail

    def test_assign_group_group_roles_not_found(self, mock_actor, mock_iam_error):
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        mock_iam_data = mock_iam_error(
            error_type=GroupNotFound,
            message=f"Group not found with id :{str(group_id)}",
        )

        group_roles = schemas.AssignGroupRoles(roles=ASSIGN_GROUP_ROLES)
        with pytest.raises(HTTPException) as exc_info:
            assign_group_roles(
                group_id=group_id,
                group_roles=group_roles,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Group not found" in exc_info.value.detail

    def test_assign_group_roles_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We couldn't process your request.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        group_roles = schemas.AssignGroupRoles(roles=ASSIGN_GROUP_ROLES)
        with pytest.raises(HTTPException) as exc_info:
            assign_group_roles(
                group_id=group_id,
                group_roles=group_roles,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process your request." in exc_info.value.detail

    def test_assign_group_roles_unauthorized_user(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=UnauthorizedUser,
            message="Unauthorized access.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        group_roles = schemas.AssignGroupRoles(roles=ASSIGN_GROUP_ROLES)
        with pytest.raises(HTTPException) as exc_info:
            assign_group_roles(
                group_id=group_id,
                group_roles=group_roles,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Unauthorized access." in exc_info.value.detail

    def test_assign_group_roles_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )
        group_id = UUID4("9eab741e-4b45-4143-9c1a-ff60711e4add")
        group_roles = schemas.AssignGroupRoles(roles=ASSIGN_GROUP_ROLES)
        with pytest.raises(HTTPException) as exc_info:
            assign_group_roles(
                group_id=group_id,
                group_roles=group_roles,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail

    # Test Case 1: Successful request with "DistributorAdmin" role
    def test_group_by_parent_path_distributor_admin(self, mock_iam, mock_actor):
        group_role_obj = schemas.GroupRolesDetails(
            id="4e42f62b-9190-464c-a753-061cfea6590b",
            name="Nextgen Clearing",
            path="/Nextgen Clearing",
            attributes={
                "organization_type": ["DISTRIBUTOR"],
                "type": ["organization"],
                "organization_id": ["1"],
            },
            realmRoles=[],
            clientRoles={},
            subGroups=[
                {
                    "id": "d78962b2-1a00-41c9-a966-b62b235a64a9",
                    "name": "2l9b3xseyq",
                    "path": "/Nextgen Clearing/2l9b3xseyq",
                    "attributes": {
                        "organization_type": ["CLIENT"],
                        "type": ["organization"],
                        "organization_id": ["1835"],
                    },
                    "realmRoles": [],
                    "clientRoles": {},
                    "subGroups": [],
                },
            ],
        )
        mock_iam.groups.get_subgroup_by_group = MagicMock(return_value=group_role_obj)
        group_id = "d78962b2-1a00-41c9-a966-b62b235a64a9"
        response = subgroup_by_group(group_id=group_id, iam=mock_iam, actor=mock_actor)
        assert response is not None
        assert response.id == UUID4("4e42f62b-9190-464c-a753-061cfea6590b")
        assert response.name == "Nextgen Clearing"
        assert response.path == "/Nextgen Clearing"
        assert response.attributes == {
            "organization_id": ["1"],
            "organization_type": ["DISTRIBUTOR"],
            "type": ["organization"],
        }
        assert response.realmRoles == []
        assert response.clientRoles == {}

    # Test Case 2: Successful request with a non-"DistributorAdmin" role

    def test_group_by_parent_path_non_admin(self, mock_iam, mock_actor_without_role):
        group_id = "d78962b2-1a00-41c9-a966-b62b235a64a9"
        with pytest.raises(HTTPException) as exc_info:
            subgroup_by_group(
                group_id=group_id, iam=mock_iam, actor=mock_actor_without_role
            )
        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert exc_info.value.detail == "You are trying to access a restricted area."

    # Test Case 3: Successful request, but the specified group path is not found
    def test_group_by_parent_path_group_not_found(self, mock_iam, mock_actor):
        group_id = "d78962b2-1a00-41c9-a966-b62b235a64a9"
        mock_iam.groups.get_subgroup_by_group.side_effect = GroupNotFound
        with pytest.raises(HTTPException) as exc_info:
            subgroup_by_group(group_id=group_id, iam=mock_iam, actor=mock_actor)
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    # Test Case 4: Successful request, but IAM service raises a ValueError
    def test_group_by_parent_path_value_error(self, mock_iam, mock_actor):
        group_id = "d78962b2-1a00-41c9-a966-b62b235a64a9"
        mock_iam.groups.get_subgroup_by_group.side_effect = ValueError
        with pytest.raises(HTTPException) as exc_info:
            subgroup_by_group(group_id=group_id, iam=mock_iam, actor=mock_actor)
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

    # Test Case 5: Successful request, but an unexpected exception is raised
    def test_group_by_parent_path_unexpected_exception(self, mock_iam, mock_actor):
        group_id = "d78962b2-1a00-41c9-a966-b62b235a64a9"
        mock_iam.groups.get_subgroup_by_group.side_effect = Exception
        with pytest.raises(HTTPException) as exc_info:
            subgroup_by_group(group_id=group_id, iam=mock_iam, actor=mock_actor)
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

    def test_get_group_and_subgroup_success(self, mock_actor, mock_iam):
        response = get_group_and_subgroup(
            iam=mock_iam,
            actor=mock_actor,
        )
        response_schema = schemas.GroupsResponse(result=GROUPS)
        assert response.result == response_schema.result

    def test_get_group_and_subgroup_group_not_found(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=GroupNotFound,
            message="Groups not found.",
        )

        with pytest.raises(HTTPException) as exc_info:
            get_group_and_subgroup(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Group details not found." in exc_info.value.detail

    def test_get_group_and_subgroup_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We couldn't process your request.",
        )
        with pytest.raises(HTTPException) as exc_info:
            get_group_and_subgroup(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process your request." in exc_info.value.detail

    def test_get_group_and_subgroup_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )
        with pytest.raises(HTTPException) as exc_info:
            get_group_and_subgroup(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail
