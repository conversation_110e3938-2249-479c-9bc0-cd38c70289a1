from unittest.mock import MagicMock, patch
from uuid import UUID

import pytest
from fastapi import HTTP<PERSON>x<PERSON>, status

from app import schemas
from app.adapters.iam.base import AbstractIAM, AbstractUsersAPI
from app.adapters.iam.exc import UnauthorizedUser, UsersNotFound
from app.adapters.iam.schemas import (
    GroupMemberResponse,
    Groups,
    RoleBaseResponse,
    UserScope,
    UserScopeList,
)
from app.api.api_v1.endpoints.users import (
    get_evaluated_user_scope,
    get_users_scopes,
    read_authenticated_user,
    users,
)
from app.api.api_v1.examples import (
    ACTOR_DETAILS,
    EVALUATE_SCOPE,
    TOKEN,
    USER_DETAILS,
    USER_INFO_RESPONSE,
)
from app.schemas.auth import RealmAccess, UserInfo
from app.services import mappers


@pytest.fixture
def mock_actor():
    roles = [
        "ClinetAdmin",
        "create-realm",
        "default-roles-master",
        "offline_access",
        "admin",
        "DistributorAdmin",
        "uma_authorization",
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_iam():
    mock_iam = MagicMock(spec=AbstractIAM)
    mock_iam.users = MagicMock(spec=AbstractUsersAPI)
    mock_iam.users_admin = MagicMock(spec=AbstractUsersAPI)
    mock_iam.users.get_evaluated_user_scope.return_value = schemas.TokenResponse(
        access_token=TOKEN
    )
    mock_iam.users_admin.get_users.return_value = GroupMemberResponse(
        result=USER_DETAILS
    )
    return mock_iam


@pytest.fixture
def mock_iam_error():
    def auth_factory(error_type, message):
        mock_iam = MagicMock(spec=AbstractIAM)
        mock_iam.users = MagicMock(spec=AbstractUsersAPI)
        mock_iam.users_admin = MagicMock(spec=AbstractUsersAPI)
        mock_iam.users.get_evaluated_user_scope.return_value = schemas.TokenResponse(
            access_token=TOKEN
        )
        mock_iam.users_admin.get_users.return_value = GroupMemberResponse(
            result=USER_DETAILS
        )
        mock_iam.users.get_evaluated_user_scope.side_effect = error_type(message)
        mock_iam.users_admin.get_users.side_effect = error_type(message)
        return mock_iam

    return auth_factory


class TestUsers:
    def test_user_scope_success(self, mock_actor, mock_iam):
        response = get_evaluated_user_scope(
            iam=mock_iam,
            actor=mock_actor,
        )

        assert response.access_token == TOKEN

    def test_user_scope_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We couldn't process your request",
        )
        with pytest.raises(HTTPException) as exc_info:
            get_evaluated_user_scope(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process your request" in exc_info.value.detail

    def test_user_scope_unauthorized(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=UnauthorizedUser,
            message="Unauthorized access.",
        )
        with pytest.raises(HTTPException) as exc_info:
            get_evaluated_user_scope(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Unauthorized access." in exc_info.value.detail

    def test_user_scope_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )
        with pytest.raises(HTTPException) as exc_info:
            get_evaluated_user_scope(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail

    def test_user_me_success(self, mock_actor):
        mapper = MagicMock(spec=mappers)
        mock_iam = MagicMock(spec=AbstractIAM)
        mock_iam.users = MagicMock()
        group = [
            Groups(
                id=UUID("c03ba3ad-929e-4d41-9796-ff65b771efb4"),
                name="Nextgen Clearing",
                path="/Nextgen Clearing",
                subGroups=[],
            )
        ]
        roles = [
            RoleBaseResponse(
                id=UUID("5d314d82-4ba3-462a-a2b0-cec69ad2fc14"),
                name="default-roles-spog-local",
                description="${role_default-roles}",
                composite=True,
                clientRole=False,
                containerId="spog-local",
                userCount=1,
            ),
        ]
        mock_iam.users.get_user_assigned_roles.return_value = roles
        mock_iam.users.get_user_assigned_groups.return_value = group
        mapper.user_info_to_user.return_value = schemas.User(**USER_INFO_RESPONSE)
        response = read_authenticated_user(
            iam=mock_iam,
            user_info=mock_actor,
        )
        assert response == schemas.User(**USER_INFO_RESPONSE)

    def test_get_users_scopes(self, mock_actor, mock_iam):
        mock_iam.users.get_users_scopes.return_value = [UserScope(**EVALUATE_SCOPE)]
        response = get_users_scopes(
            iam=mock_iam,
            user_info=mock_actor,
        )
        expected_result = UserScopeList(result=[UserScope(**EVALUATE_SCOPE)])
        assert response == expected_result

    def test_get_users_scopes_value_error(self):
        with pytest.raises(HTTPException) as exc_info:
            with patch(
                "app.adapters.iam.keycloak.KeycloakUsersAPI.get_users_scopes",
                side_effect=ValueError("value error"),
            ):
                get_users_scopes(iam=mock_iam, user_info=None)
        assert exc_info.value.status_code == 400
        assert "Couldn't process the request." in exc_info.value.detail

    def test_read_authenticated_user_value_error(self):
        with pytest.raises(HTTPException) as exc_info:
            with patch(
                "app.services.mappers.user_info_to_user",
                side_effect=ValueError("Something went wrong"),
            ):
                read_authenticated_user(user_info=None)
        assert exc_info.value.status_code == 400
        assert "Couldn't process the request." in exc_info.value.detail

    def test_read_authenticated_user_exception(self, mock_actor):
        with pytest.raises(HTTPException) as exc_info:
            user_info = mock_actor
            with patch(
                "app.services.mappers.user_info_to_user",
                side_effect=Exception("Something went wrong"),
            ):
                read_authenticated_user(user_info=user_info)

        assert exc_info.value.status_code == 400
        assert "Couldn't process the request." in exc_info.value.detail

    def test_users_success(self, mock_actor, mock_iam):
        page = 0
        page_size = 10
        response = users(
            page=page,
            page_size=page_size,
            iam=mock_iam,
            actor=mock_actor,
        )
        actual_schema = GroupMemberResponse(result=USER_DETAILS)
        assert response.result == actual_schema.result

    def test_users_users_not_found(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=UsersNotFound,
            message="User not found.",
        )
        with pytest.raises(HTTPException) as exc_info:
            users(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "User not found." in exc_info.value.detail

    def test_users_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We couldn't process your request.",
        )
        with pytest.raises(HTTPException) as exc_info:
            users(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process your request." in exc_info.value.detail

    def test_users_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="We couldn't process your request.",
        )
        with pytest.raises(HTTPException) as exc_info:
            users(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

    def test_users_page_size_invalid(self, mock_actor, mock_iam):
        page = 0
        page_size = 4
        with pytest.raises(HTTPException) as exc_info:
            users(
                page=page,
                page_size=page_size,
                iam=mock_iam,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "max Number should be a multiple of 10"
