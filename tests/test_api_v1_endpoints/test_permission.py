from unittest.mock import Magic<PERSON>ock

import pytest
from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>, status

from app.adapters.iam import schemas
from app.adapters.iam.base import AbstractClaimPermissionAPI, AbstractIAM
from app.api.api_v1.endpoints.claims import create_permission
from app.api.api_v1.examples import (
    ACTOR_DETAILS,
    PERMISSION_SCOPE_RESPONSE,
    SCOPE_BASED_PERMISSION,
)
from app.schemas.auth import RealmAccess, UserInfo


@pytest.fixture
def mock_actor():
    roles = [
        "ClinetAdmin",
        "create-realm",
        "default-roles-master",
        "offline_access",
        "admin",
        "DistributorAdmin",
        "uma_authorization",
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_actor_error():
    roles = [
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_claim_permission_data():
    mock_claim_permission_data = schemas.ClaimPermission(**SCOPE_BASED_PERMISSION)
    return mock_claim_permission_data


@pytest.fixture
def mock_iam():
    mock_iam = MagicMock(spec=AbstractIAM)
    mock_iam.claim_permission_admin = MagicMock(spec=AbstractClaimPermissionAPI)
    mock_iam.claim_permission_admin.create_permission.return_value = (
        schemas.ClaimPermissionResponse(**PERMISSION_SCOPE_RESPONSE)
    )
    return mock_iam


@pytest.fixture
def mock_iam_error():
    def auth_factory(error_type, message):
        mock_iam = MagicMock(spec=AbstractIAM)
        mock_iam.claim_permission_admin = MagicMock(spec=AbstractClaimPermissionAPI)
        mock_iam.claim_permission_admin.create_permission.return_value = (
            schemas.ClaimPermissionResponse(**PERMISSION_SCOPE_RESPONSE)
        )
        mock_iam.claim_permission_admin.create_permission.side_effect = error_type(
            message
        )
        return mock_iam

    return auth_factory


class TestPermissions:
    def test_create_permission_success(
        self, mock_actor, mock_claim_permission_data, mock_iam
    ):

        response = create_permission(
            claim_permission_data=mock_claim_permission_data,
            iam=mock_iam,
            actor=mock_actor,
        )
        assert response.name == "Test Permission"

    def test_create_permission_value_error(
        self, mock_actor, mock_claim_permission_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We Couldn't process your request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_permission(
                claim_permission_data=mock_claim_permission_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We Couldn't process your request." in exc_info.value.detail

    def test_create_permission_exception(
        self, mock_actor, mock_claim_permission_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_permission(
                claim_permission_data=mock_claim_permission_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request" in exc_info.value.detail

    def test_create_permission_forbidden(
        self, mock_actor_error, mock_claim_permission_data, mock_iam
    ):

        with pytest.raises(HTTPException) as exc_info:
            create_permission(
                claim_permission_data=mock_claim_permission_data,
                iam=mock_iam,
                actor=mock_actor_error,
            )
        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert "You are trying to access a restricted area." in exc_info.value.detail
