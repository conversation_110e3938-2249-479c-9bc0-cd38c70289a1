from typing import Iterator

import pytest
from fastapi.testclient import Test<PERSON>lient
from starlette import status

from app.api.health_check import HEALTH_CHECK_SECRET_HEADER
from app.core.config import settings
from app.main import app


@pytest.fixture(scope="session")
def client() -> TestClient | Iterator[TestClient]:
    """A test client for the API"""
    with TestClient(app) as c:
        yield c


def test_ping_url_path(client):
    url = client.app.url_path_for("ping")
    assert url == "/v1/authz/ping"


def test_healthy_url_path(client):
    url = client.app.url_path_for("healthy")
    assert url == "/v1/authz/healthy"


@pytest.mark.parametrize("url_name", ["ping", "healthy"])
def test_health_check_with_secret_is_ok(client, url_name):
    url = client.app.url_path_for("ping")
    response = client.get(
        url,
        headers={HEALTH_CHECK_SECRET_HEADER: settings.HEALTH_CHECK_SECRET},
    )
    assert response.status_code == status.HTTP_200_OK
