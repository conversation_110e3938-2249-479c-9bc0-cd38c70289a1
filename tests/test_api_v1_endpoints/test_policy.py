from unittest.mock import MagicMock

import pytest
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status

from app.adapters.iam import schemas
from app.adapters.iam.base import AbstractIAM, AbstractRolesAPI
from app.adapters.iam.exc import PolicyNotFound
from app.api.api_v1.endpoints.policy import get_policy
from app.api.api_v1.examples import ACTOR_DETAILS, POLICIES_LIST
from app.schemas.auth import RealmAccess, UserInfo


@pytest.fixture
def mock_actor():
    roles = [
        "ClinetAdmin",
        "create-realm",
        "default-roles-master",
        "offline_access",
        "admin",
        "DistributorAdmin",
        "uma_authorization",
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_actor_error():
    roles = [
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_iam():
    mock_iam = MagicMock(spec=AbstractIAM)
    mock_iam.roles = MagicMock(spec=AbstractRolesAPI)
    mock_iam.roles.get_policy_list.return_value = schemas.PolicyList(
        result=POLICIES_LIST
    )
    return mock_iam


@pytest.fixture
def mock_iam_error():
    def auth_factory(error_type, message):
        mock_iam = MagicMock(spec=AbstractIAM)
        mock_iam.roles = MagicMock(spec=AbstractRolesAPI)
        mock_iam.roles.get_policy_list.return_value = schemas.PolicyList(
            result=POLICIES_LIST
        )
        mock_iam.roles.get_policy_list.side_effect = error_type(message)
        return mock_iam

    return auth_factory


class TestPolicy:
    def test_get_policy_success(self, mock_actor, mock_iam):
        policy_list_len = len(POLICIES_LIST)
        response = get_policy(
            iam=mock_iam,
            actor=mock_actor,
        )

        assert len(response.result) == policy_list_len

    def test_get_policy_not_found(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=PolicyNotFound,
            message="Policy not found.",
        )

        with pytest.raises(HTTPException) as exc_info:
            get_policy(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Policy not found." in exc_info.value.detail

    def test_get_policy_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We couldn't process your request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            get_policy(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process your request." in exc_info.value.detail

    def test_get_policy_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            get_policy(
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail
