import uuid
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>

import pytest
from fastapi import HTTP<PERSON>x<PERSON>
from httpx import Request, Response
from keycloak_client import <PERSON><PERSON>loakConflict
from pydantic import UUID4
from starlette import status

from app.adapters.iam.base import AbstractIAM, AbstractResourceAPI
from app.adapters.iam.exc import (
    ForbiddenError,
    PermissionNotFound,
    ResourceAlreadyExist,
    ResourceNotFound,
    ScopeAlreadyExist,
    ScopeNotFound,
    UnauthorizedUser,
)
from app.adapters.iam.schemas import ResourceList, ResourceResponse
from app.api.api_v1.endpoints.resources import (
    create_resource,
    delete_resource_by_id,
    get_policy_by_permission,
    get_resource_scope_permission,
    update_resource,
)
from app.api.api_v1.examples import ACTOR_DETAILS, SCOE_UUID_LIST
from app.schemas.auth import RealmAccess, UserInfo

# from auth.exceptions import ConflictException, ForbiddenError


@pytest.fixture
def mock_actor():
    roles = [
        "ClinetAdmin",
        "create-realm",
        "default-roles-master",
        "offline_access",
        "admin",
        "DistributorAdmin",
        "uma_authorization",
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_actor_error():
    roles = [
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_iam():
    mock_iam = MagicMock(spec=AbstractIAM)
    mock_iam.resources_admin = MagicMock(spec=AbstractResourceAPI)
    mock_iam.resources_admin.delete_resource_by_id.return_value = None
    return mock_iam


@pytest.fixture
def mock_iam_error():
    def auth_factory(error_type, message):
        mock_iam = MagicMock(spec=AbstractIAM)
        mock_iam.resources_admin = MagicMock(spec=AbstractResourceAPI)
        mock_iam.resource = MagicMock(spec=AbstractResourceAPI)
        mock_iam.resources_admin.delete_resource_by_id.return_value = None
        mock_iam.resources_admin.delete_resource_by_id.side_effect = error_type(message)
        mock_iam.resources_admin.update_resource.return_value = None
        mock_iam.resources_admin.update_resource.side_effect = error_type(message)

        mock_iam.resource.update_resource.return_value = None
        mock_iam.resource.update_resource.side_effect = error_type(message)
        return mock_iam

    return auth_factory


iam = Mock()
actor = Mock(realm_access=Mock(roles=["DistributorAdmin"]))
user_actor = Mock(realm_access=Mock(roles=["ClientUser"]))

policy_object = [
    {
        "id": "6c7620c5-7efb-4503-bbf8-bb002c36d9d1",
        "name": "ClientAdmin",
        "description": "ClientAdmin",
        "type": "role",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {"key1": "value1", "key2": "value2"},
    },
    {
        "id": "51f130cf-35c2-4d86-97f2-e787b42452fb",
        "name": "Test Role",
        "description": "Test Role",
        "type": "role",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {},
    },
]
resource_obj = [
    {
        "name": "AccountManagement",
        "owner": {
            "id": "55bed2c1-b35b-4704-aa6e-02afc4261eaa",
            "name": "organization-management",
        },
        "ownerManagedAccess": False,
        "_id": "06f2479e-0de4-45ba-8c74-22a6f92f2a70",
        "displayName": "AccountManagement",
        "attributes": {},
        "uris": [],
        "resource_scopes": [],
        "scopes": [
            {
                "id": "a2b77ba4-fcb0-4694-9de8-51ebf5c8e42c",
                "name": "list_accounts",
                "permission": [
                    {
                        "id": "67c9ea44-b158-4287-b1a3-f94e49527470",
                        "name": "list_accounts",
                        "title": "List Accounts",
                    }
                ],
            },
            {
                "id": "4fabd459-5b66-4ebc-be1c-5970365c32da",
                "name": "create_account",
                "permission": [
                    {
                        "id": "cc1faf79-8ef2-4477-8029-8e22c8ccb9eb",
                        "name": "create_account",
                        "title": "Create Account",
                    }
                ],
            },
            {
                "id": "ef2c3756-3c12-4cad-93e4-0ef1e0049f5b",
                "name": "get_account_names",
                "permission": [
                    {
                        "id": "********-6f04-4c76-845e-fb183925c739",
                        "name": "get_account_names",
                        "title": "Get Account Names",
                    }
                ],
            },
        ],
    }
]


def test_get_policy_by_permission_success():
    permission_id = uuid.uuid4()
    iam.resources_admin.get_policies_by_permission = MagicMock(
        return_value=policy_object
    )
    response = get_policy_by_permission(
        iam=iam, actor=actor, permission_id=permission_id
    )
    assert response.result[0].id == "6c7620c5-7efb-4503-bbf8-bb002c36d9d1"
    assert response.result[0].name == "ClientAdmin"
    assert response.result[0].description == "ClientAdmin"
    assert response.result[0].policyType == "role"
    assert response.result[0].config == {"key1": "value1", "key2": "value2"}
    assert response.result[0].logic == "POSITIVE"


def test_get_policy_by_permission_bad_request():
    permission_id = uuid.uuid4()
    iam.resources_admin.get_policies_by_permission.side_effect = ValueError()
    with pytest.raises(HTTPException) as exc:
        get_policy_by_permission(iam=iam, actor=actor, permission_id=permission_id)
    assert exc.value.status_code == status.HTTP_400_BAD_REQUEST


def test_get_policy_by_permission_not_found():
    permission_id = uuid.uuid4()
    iam.resources_admin.get_policies_by_permission.side_effect = PermissionNotFound()
    with pytest.raises(HTTPException) as exc:
        get_policy_by_permission(iam=iam, actor=actor, permission_id=permission_id)

    assert exc.value.status_code == status.HTTP_404_NOT_FOUND


def test_get_policy_by_permission_forbidden():
    permission_id = uuid.uuid4()
    with pytest.raises(HTTPException) as exc:
        get_policy_by_permission(iam=iam, actor=user_actor, permission_id=permission_id)
    assert exc.value.status_code == status.HTTP_403_FORBIDDEN


def test_get_policy_by_permission_generic_error():
    permission_id = uuid.uuid4()
    iam.resources_admin.get_policies_by_permission.side_effect = Exception()
    with pytest.raises(HTTPException) as exc:
        get_policy_by_permission(iam=iam, actor=actor, permission_id=permission_id)
    assert exc.value.status_code == status.HTTP_400_BAD_REQUEST
    assert exc.value.detail == "Couldn't process the request"


# Test cases for create resource and scope
def test_create_resource_201_created():
    mock_create_resources = MagicMock(return_value=ResourceResponse(**resource_obj[0]))
    iam.resource.create_resources = mock_create_resources
    response = create_resource(resouce_data=resource_obj, iam=iam, actor=actor)
    assert isinstance(response, ResourceResponse)
    assert response.name == "AccountManagement"


def test_create_resource_403_forbidden():
    with pytest.raises(HTTPException) as exc:
        create_resource(resouce_data=resource_obj, iam=iam, actor=user_actor)
    assert exc.value.status_code == status.HTTP_403_FORBIDDEN
    assert exc.value.detail == "You are trying to access a restricted area."


def test_create_resource_400_bad_request():
    iam.resource.create_resources.side_effect = ResourceNotFound(
        "Couldn't process the request."
    )

    with pytest.raises(HTTPException) as exc:
        create_resource(resouce_data=resource_obj, iam=iam, actor=actor)

    assert exc.value.status_code == status.HTTP_400_BAD_REQUEST
    assert exc.value.detail == "Couldn't process the request."


def test_create_resource_409_conflict():
    exception_message = "Conflict"
    mock_response = Response(
        status_code=409,
        request=Request("GET", "http://example.com"),
    )
    iam.resource.create_resources.side_effect = KeycloakConflict(
        message=exception_message,
        request=mock_response.request,
        response=mock_response,
    )

    with pytest.raises(HTTPException) as exc:
        create_resource(resouce_data=resource_obj, iam=iam, actor=actor)

    assert exc.value.status_code == status.HTTP_409_CONFLICT
    assert exc.value.detail == "Resource is already created."


def test_create_resource_400_bad_request_generic_error():
    iam.resource.create_resources.side_effect = Exception(
        "Couldn't process the request."
    )
    with pytest.raises(HTTPException) as exc:
        create_resource(resouce_data=resource_obj, iam=iam, actor=actor)
    assert exc.value.status_code == status.HTTP_400_BAD_REQUEST
    assert exc.value.detail == "Couldn't process the request."


def test_create_resource_400_value_error():
    iam.resource.create_resources.side_effect = ValueError()
    with pytest.raises(HTTPException) as exc:
        create_resource(resouce_data=resource_obj, iam=iam, actor=actor)
    assert exc.value.status_code == status.HTTP_400_BAD_REQUEST
    assert exc.value.detail == "We couldn't process the request."


def test_create_resource_scope_already_exist():
    iam.resource.create_resources.side_effect = ScopeAlreadyExist(
        "Scope already exist."
    )
    with pytest.raises(HTTPException) as exc:
        create_resource(resouce_data=resource_obj, iam=iam, actor=actor)
    assert exc.value.status_code == status.HTTP_409_CONFLICT
    assert exc.value.detail == "Scope already exist."


def test_create_resource_resource_already_exist():
    iam.resource.create_resources.side_effect = ResourceAlreadyExist(
        "Resource already exist."
    )
    with pytest.raises(HTTPException) as exc:
        create_resource(resouce_data=resource_obj, iam=iam, actor=actor)
    assert exc.value.status_code == status.HTTP_409_CONFLICT
    assert exc.value.detail == "Resource already exist."


def test_update_resource_403_forbidden(mock_actor_error, mock_iam_error):
    mock_iam_data = mock_iam_error(
        error_type=ForbiddenError,
        message="Permission not found.",
    )
    resource_id = "06f2479e-0de4-45ba-8c74-22a6f92f2a70"
    resource_scopes = SCOE_UUID_LIST
    with pytest.raises(HTTPException) as exc:
        update_resource(
            resource_id=resource_id,
            resource_scopes=resource_scopes,
            iam=mock_iam_data,
            actor=mock_actor_error,
        )
    assert exc.value.status_code == status.HTTP_403_FORBIDDEN
    assert exc.value.detail == "You are trying to access a restricted area."


def test_update_resource_value_error(mock_actor, mock_iam_error):
    mock_iam_data = mock_iam_error(
        error_type=ValueError,
        message="We couldn't process the request.",
    )
    resource_id = "06f2479e-0de4-45ba-8c74-22a6f92f2a70"
    resource_scopes = SCOE_UUID_LIST
    with pytest.raises(HTTPException) as exc:
        update_resource(
            resource_id=resource_id,
            resource_scopes=resource_scopes,
            iam=mock_iam_data,
            actor=mock_actor,
        )
    assert exc.value.status_code == status.HTTP_400_BAD_REQUEST
    assert exc.value.detail == "We couldn't process the request."


def test_update_resource_resource_not_found(mock_actor, mock_iam_error):
    mock_iam_data = mock_iam_error(
        error_type=ResourceNotFound,
        message="Resource not found while updating resource.",
    )
    resource_id = "06f2479e-0de4-45ba-8c74-22a6f92f2a70"
    resource_scopes = SCOE_UUID_LIST
    with pytest.raises(HTTPException) as exc:
        update_resource(
            resource_id=resource_id,
            resource_scopes=resource_scopes,
            iam=mock_iam_data,
            actor=mock_actor,
        )
    assert exc.value.status_code == status.HTTP_404_NOT_FOUND
    assert exc.value.detail == "Resource not found while updating resource."


def test_update_resource_scope_already_exist(mock_actor, mock_iam_error):
    mock_iam_data = mock_iam_error(
        error_type=ScopeAlreadyExist,
        message="Scope already exist.",
    )
    resource_id = "06f2479e-0de4-45ba-8c74-22a6f92f2a70"
    resource_scopes = SCOE_UUID_LIST
    with pytest.raises(HTTPException) as exc:
        update_resource(
            resource_id=resource_id,
            resource_scopes=resource_scopes,
            iam=mock_iam_data,
            actor=mock_actor,
        )
    assert exc.value.status_code == status.HTTP_409_CONFLICT
    assert exc.value.detail == "Scope already exist."


def test_update_resource_unauthorized_access(mock_actor, mock_iam_error):
    mock_iam_data = mock_iam_error(
        error_type=UnauthorizedUser,
        message="Unauthorized access.",
    )
    resource_id = "06f2479e-0de4-45ba-8c74-22a6f92f2a70"
    resource_scopes = SCOE_UUID_LIST
    with pytest.raises(HTTPException) as exc:
        update_resource(
            resource_id=resource_id,
            resource_scopes=resource_scopes,
            iam=mock_iam_data,
            actor=mock_actor,
        )
    assert exc.value.status_code == status.HTTP_401_UNAUTHORIZED
    assert exc.value.detail == "Unauthorized access."


def test_update_resource_exception(mock_actor, mock_iam_error):
    mock_iam_data = mock_iam_error(
        error_type=Exception,
        message="Couldn't process the request",
    )
    resource_id = "06f2479e-0de4-45ba-8c74-22a6f92f2a70"
    resource_scopes = SCOE_UUID_LIST
    with pytest.raises(HTTPException) as exc:
        update_resource(
            resource_id=resource_id,
            resource_scopes=resource_scopes,
            iam=mock_iam_data,
            actor=mock_actor,
        )
    assert exc.value.status_code == status.HTTP_400_BAD_REQUEST
    assert exc.value.detail == "Couldn't process the request"


# Test cases for get_resource_scope_permission

# Test case for a successful execution
def test_get_resource_scope_permission_success():
    expected_result = ResourceList(result=resource_obj)
    iam.resources_admin.get_resource_scopes_and_permissions = MagicMock(
        return_value=resource_obj
    )
    response = get_resource_scope_permission(iam=iam)
    assert isinstance(response, ResourceList)
    response.result == expected_result.result


# Test case for a 400 Bad Request
def test_get_resource_scope_permission_bad_request():
    iam.resources_admin.get_resource_scope_permission.side_effect = ValueError()

    with pytest.raises(HTTPException) as exc_info:
        get_resource_scope_permission()

    assert exc_info.value.status_code == 400
    assert exc_info.value.detail == "Couldn't process the request"


# Test case for a 404 Not Found
@pytest.mark.skip("Expected 404 raised 400")
def test_get_resource_scope_permission_not_found():
    iam.resources_admin.get_resource_scope_permission.side_effect = ResourceNotFound

    with pytest.raises(HTTPException) as exc_info:
        get_resource_scope_permission()

    assert exc_info.value.status_code == 404
    assert exc_info.value.detail == "Couldn't process the request"


# Test case for a 403 Forbidden
@pytest.mark.skip("Not Required")
def test_get_resource_scope_permission_forbidden():
    iam.resources_admin.get_resource_scope_permission.side_effect = UnauthorizedUser

    with pytest.raises(HTTPException) as exc_info:
        get_resource_scope_permission()

    assert exc_info.value.status_code == 403


# Test case for a generic Exception
def test_get_resource_scope_permission_generic_exception(caplog):
    iam.resources_admin.get_resource_scope_permission.side_effect = Exception

    with pytest.raises(HTTPException) as exc_info:
        get_resource_scope_permission()

    assert exc_info.value.status_code == 400
    assert "Couldn't process the request" in str(exc_info.value.detail)
    assert "Depends' object has no attribute 'resources_admin" in caplog.text


class TestResources:
    def test_delete_resource_by_id_success(self, mock_actor, mock_iam):
        resource_id = UUID4("be52a98c-8b47-4c6b-b586-29d68d875a63")
        response = delete_resource_by_id(
            resource_id=resource_id,
            iam=mock_iam,
            actor=mock_actor,
        )
        response_schema = None
        assert response == response_schema

    def test_delete_resource_by_id_resource_not_found(self, mock_actor, mock_iam_error):
        resource_id = UUID4("be52a98c-8b47-4c6b-b586-29d68d875a63")
        mock_iam_data = mock_iam_error(
            error_type=ResourceNotFound,
            message=f"Resource not found while getting with Id: {str(resource_id)}",
        )
        with pytest.raises(HTTPException) as exc_info:
            delete_resource_by_id(
                resource_id=resource_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Resource not found." in exc_info.value.detail

    def test_delete_resource_by_id_scope_not_found(self, mock_actor, mock_iam_error):
        resource_id = UUID4("be52a98c-8b47-4c6b-b586-29d68d875a63")
        mock_iam_data = mock_iam_error(
            error_type=ScopeNotFound,
            message="Scope not found while deleting the resource.",
        )
        with pytest.raises(HTTPException) as exc_info:
            delete_resource_by_id(
                resource_id=resource_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Scope not found while deleting the resource." in exc_info.value.detail

    def test_delete_resource_by_id_unauthorized_access(
        self, mock_actor, mock_iam_error
    ):
        resource_id = UUID4("be52a98c-8b47-4c6b-b586-29d68d875a63")
        mock_iam_data = mock_iam_error(
            error_type=UnauthorizedUser,
            message="Unauthorized access.",
        )
        with pytest.raises(HTTPException) as exc_info:
            delete_resource_by_id(
                resource_id=resource_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Unauthorized access." in exc_info.value.detail

    def test_group_members_forbidden(self, mock_actor_error, mock_iam):
        resource_id = UUID4("be52a98c-8b47-4c6b-b586-29d68d875a63")
        with pytest.raises(HTTPException) as exc_info:
            delete_resource_by_id(
                resource_id=resource_id,
                iam=mock_iam,
                actor=mock_actor_error,
            )
        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert "You are trying to access a restricted area." in exc_info.value.detail

    def test_delete_resource_by_id_value_error(self, mock_actor, mock_iam_error):
        resource_id = UUID4("be52a98c-8b47-4c6b-b586-29d68d875a63")
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We couldn't process your request.",
        )
        with pytest.raises(HTTPException) as exc_info:
            delete_resource_by_id(
                resource_id=resource_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process your request." in exc_info.value.detail

    def test_delete_resource_by_id_exception(self, mock_actor, mock_iam_error):
        resource_id = UUID4("be52a98c-8b47-4c6b-b586-29d68d875a63")
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )
        with pytest.raises(HTTPException) as exc_info:
            delete_resource_by_id(
                resource_id=resource_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail
