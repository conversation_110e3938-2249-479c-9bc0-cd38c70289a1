from unittest.mock import Mock

import pytest
from fastapi import HTTPException
from starlette import status

from app.adapters.iam import schemas
from app.adapters.iam.exc import RoleNotFound
from app.api.api_v1.endpoints.client_roles import get_client_role


class MockIAMAuth:
    def get_client_roles(self):
        return [
            {
                "id": "340bd034-06af-485b-befc-1b1517ea896b",
                "name": "Test_spog_role",
                "description": "Test_spog_role",
                "composite": False,
                "clientRole": True,
                "containerId": "8c06012b-28ef-4a60-b81a-09d46802a776",
            },
            {
                "id": "d7c90d06-eae8-4cd1-8f2b-88505b5b50b9",
                "name": "uma_protection",
                "description": None,
                "composite": False,
                "clientRole": True,
                "containerId": "8c06012b-28ef-4a60-b81a-09d46802a776",
            },
        ]

    @property
    def clients(self):
        return Mock(get_client_roles=self.get_client_roles)


def test_get_roles_valid():
    mock_iam = MockIAMAuth()
    actor = Mock(realm_access=Mock(roles=["DistributorAdmin"]))
    result = get_client_role(iam=mock_iam, actor=actor)
    expected_result = [
        {
            "id": "340bd034-06af-485b-befc-1b1517ea896b",
            "name": "Test_spog_role",
            "description": "Test_spog_role",
            "composite": False,
            "clientRole": True,
            "containerId": "8c06012b-28ef-4a60-b81a-09d46802a776",
        },
        {
            "id": "d7c90d06-eae8-4cd1-8f2b-88505b5b50b9",
            "name": "uma_protection",
            "description": None,
            "composite": False,
            "clientRole": True,
            "containerId": "8c06012b-28ef-4a60-b81a-09d46802a776",
        },
    ]
    expected_result_response = schemas.ClientRoleList(result=expected_result)
    assert result == expected_result_response


def test_get_roles_not_found():
    mock_iam = Mock()

    mock_iam.clients.get_client_roles.side_effect = RoleNotFound()
    mock_iam.clients.get_client_roles = Mock(side_effect=RoleNotFound)

    actor = Mock(realm_access=Mock(roles=["DistributorAdmin"]))

    with pytest.raises(HTTPException) as exc_info:
        get_client_role(iam=mock_iam, actor=actor)

    assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
    assert exc_info.value.detail == "Roles not found."


def test_get_roles_forbidden():
    actor = Mock(realm_access=Mock(roles=["User"]))
    with pytest.raises(HTTPException) as exc_info:
        get_client_role(actor=actor)
    assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN


def test_get_roles_value_error():
    mock_iam = Mock()

    mock_iam.clients.get_client_roles.side_effect = ValueError()
    mock_iam.clients.get_client_roles = Mock(side_effect=ValueError)

    actor = Mock(realm_access=Mock(roles=["DistributorAdmin"]))

    with pytest.raises(HTTPException) as exc_info:
        get_client_role(iam=mock_iam, actor=actor)

    assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
    assert exc_info.value.detail == "We couldn't process your request."


def test_get_client_role_generic_error():
    mock_iam = Mock()

    mock_iam.clients.get_client_roles.side_effect = Exception()
    mock_iam.clients.get_client_roles = Mock(side_effect=Exception)

    actor = Mock(realm_access=Mock(roles=["DistributorAdmin"]))

    with pytest.raises(HTTPException) as exc_info:
        get_client_role(iam=mock_iam, actor=actor)

    assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
    assert exc_info.value.detail == "Couldn't process the request."
