from unittest.mock import Magic<PERSON>ock

import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from pydantic import UUID4

from app.adapters.iam import schemas
from app.adapters.iam.base import (
    AbstractGroupAPI,
    AbstractIAM,
    AbstractResourceAPI,
    AbstractRolesAPI,
    AbstractUsersAPI,
)
from app.adapters.iam.exc import (
    AlreadyExist,
    DefaultRoleError,
    MaxNumberInvalid,
    PermissionNotFound,
    PolicyAlreadyExist,
    PolicyNotFound,
    RoleAlreadyExist,
    RoleNotFound,
)
from app.api.api_v1.endpoints.realm_roles import (
    create_role,
    delete_role_by_id,
    get_realm_role,
    get_role_by_id,
    update_role,
)
from app.api.api_v1.examples import (
    ACTOR_DETAILS,
    CREATE_REALM_ROLES,
    GROUP1,
    G<PERSON>UPS,
    REALM_ROLES_POLICY_RESPONSE,
    REALM_ROLES_RESPONSE,
    ROLES_LIST,
    UPDATE_REALM_ROLES,
    USER_DETAILS,
)
from app.schemas.auth import RealmAccess, UserInfo


@pytest.fixture
def mock_actor():
    roles = [
        "create-realm",
        "default-roles-master",
        "offline_access",
        "admin",
        "DistributorAdmin",
        "uma_authorization",
        "ClientUser",
    ]
    mock_realm_access = RealmAccess(roles=roles)
    mock_actor = UserInfo(**ACTOR_DETAILS, realm_access=mock_realm_access)
    return mock_actor


@pytest.fixture
def mock_realm_role_data():
    mock_realm_role_data = schemas.RoleRequest(**UPDATE_REALM_ROLES)
    return mock_realm_role_data


@pytest.fixture
def mock_create_realm_role_data():
    mock_create_realm_role_data = schemas.RolePolicyRequest(**CREATE_REALM_ROLES)
    return mock_create_realm_role_data


@pytest.fixture
def mock_iam():
    mock_iam = MagicMock(spec=AbstractIAM)
    mock_iam.roles = MagicMock(spec=AbstractRolesAPI)
    mock_iam.users = MagicMock(spec=AbstractUsersAPI)
    mock_iam.resources_admin = MagicMock(spec=AbstractResourceAPI)
    mock_iam.groups = MagicMock(spec=AbstractGroupAPI)
    mock_iam.groups.get_group_members.return_value = schemas.GroupMemberResponse(
        result=USER_DETAILS
    )
    mock_iam.groups.groups_list.return_value = schemas.GroupsResponse(result=GROUPS)
    mock_iam.roles.update_role.return_value = True
    mock_iam.roles.create_role_policy.return_value = schemas.RolePolicyResponse(
        **REALM_ROLES_POLICY_RESPONSE
    )
    mock_iam.roles.get_role_by_id.return_value = schemas.RoleResponse(
        **REALM_ROLES_RESPONSE
    )
    mock_iam.roles.get_roles_list.return_value = schemas.RolesList(result=ROLES_LIST)
    mock_iam.roles.delete_role_by_id.return_value = True
    mock_iam.roles.get_group_roles.return_value = schemas.RolesList(result=ROLES_LIST)
    mock_iam.users.get_user_assigned_groups.return_value = [schemas.Groups(**GROUP1)]
    return mock_iam


@pytest.fixture
def mock_iam_error():
    def auth_factory(error_type, message):
        mock_iam = MagicMock(spec=AbstractIAM)
        mock_iam.roles = MagicMock(spec=AbstractRolesAPI)
        mock_iam.resources_admin = MagicMock(spec=AbstractResourceAPI)
        mock_iam.roles.create_role_policy.return_value = schemas.RolePolicyResponse(
            **REALM_ROLES_POLICY_RESPONSE
        )
        mock_iam.roles.get_role_by_id.return_value = schemas.RoleResponse(
            **REALM_ROLES_RESPONSE
        )
        mock_iam.roles.get_roles_list.return_value = schemas.RolesList(
            result=ROLES_LIST
        )
        mock_iam.roles.delete_role_by_id.return_value = True

        mock_iam.roles.update_role.side_effect = error_type(message)
        mock_iam.roles.create_role_policy.side_effect = error_type(message)
        mock_iam.roles.get_role_by_id.side_effect = error_type(message)
        mock_iam.roles.get_roles_list.side_effect = error_type(message)
        mock_iam.roles.delete_role_by_id.side_effect = error_type(message)
        return mock_iam

    return auth_factory


class TestRealmRoles:
    def test_create_role_success(
        self, mock_actor, mock_create_realm_role_data, mock_iam
    ):

        response = create_role(
            role_data=mock_create_realm_role_data,
            iam=mock_iam,
            actor=mock_actor,
        )
        assert response.name == "Test"

    def test_create_roles_already_exist(
        self, mock_actor, mock_create_realm_role_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=AlreadyExist,
            message=(
                "Role and Policy with name "
                f"'{str(mock_create_realm_role_data.name)}'"
                "Already exist"
            ),
        )

        with pytest.raises(HTTPException) as exc_info:
            create_role(
                role_data=mock_create_realm_role_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert "Role and Policy Already exist" in exc_info.value.detail

    def test_create_roles_policy_already_exist(
        self, mock_actor, mock_create_realm_role_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=PolicyAlreadyExist, message="Role based Policy Already Exist."
        )

        with pytest.raises(HTTPException) as exc_info:
            create_role(
                role_data=mock_create_realm_role_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert "Role based Policy Already Exist." in exc_info.value.detail

    def test_create_roles_role_already_exist(
        self, mock_actor, mock_create_realm_role_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=RoleAlreadyExist,
            message=(
                "Role with name "
                f"[{str(mock_create_realm_role_data.name)}] Already Exist."
            ),
        )

        with pytest.raises(HTTPException) as exc_info:
            create_role(
                role_data=mock_create_realm_role_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert (
            f"Role with name [{str(mock_create_realm_role_data.name)}] Already Exist."
            in exc_info.value.detail
        )

    def test_create_roles_value_error(
        self, mock_actor, mock_create_realm_role_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We Couldn't process your request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_role(
                role_data=mock_create_realm_role_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We Couldn't process your request." in exc_info.value.detail

    def test_create_roles_exception(
        self, mock_actor, mock_create_realm_role_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_role(
                role_data=mock_create_realm_role_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail

    def test_update_role_success(self, mock_actor, mock_realm_role_data, mock_iam):
        response = update_role(
            role_id=UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6"),
            role_data=mock_realm_role_data,
            iam=mock_iam,
            actor=mock_actor,
        )
        assert response is True

    def test_update_roles_value_error(
        self, mock_actor, mock_realm_role_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We Couldn't process your request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_role(
                role_id=UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6"),
                role_data=mock_realm_role_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We Couldn't process your request." in exc_info.value.detail

    def test_update_roles_exception(
        self, mock_actor, mock_realm_role_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_role(
                role_id=UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6"),
                role_data=mock_realm_role_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail

    def test_update_roles_policy_not_found(
        self, mock_actor, mock_realm_role_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=PolicyNotFound,
            message="Policy not found.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_role(
                role_id=UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6"),
                role_data=mock_realm_role_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Policy not found." in exc_info.value.detail

    def test_update_roles_permission_not_found(
        self, mock_actor, mock_realm_role_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=PermissionNotFound,
            message="Permission not found.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_role(
                role_id=UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6"),
                role_data=mock_realm_role_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Permission not found." in exc_info.value.detail

    def test_update_roles_default_role_error(
        self, mock_actor, mock_realm_role_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=DefaultRoleError,
            message="Default role update operation not allowed.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_role(
                role_id=UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6"),
                role_data=mock_realm_role_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Default role update operation not allowed." in exc_info.value.detail

    def test_update_roles_role_not_found(
        self, mock_actor, mock_realm_role_data, mock_iam_error
    ):
        mock_iam_data = mock_iam_error(
            error_type=RoleNotFound,
            message="Role not found.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_role(
                role_id=UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6"),
                role_data=mock_realm_role_data,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Role not found." in exc_info.value.detail

    def test_get_role_by_id_success(self, mock_actor, mock_iam):
        role_id = UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6")
        response = get_role_by_id(
            role_id=role_id,
            iam=mock_iam,
            actor=mock_actor,
        )

        assert response.name == "Test"
        assert response.id == role_id

    def test_get_role_by_id_roles_not_found(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=RoleNotFound,
            message="Couldn't process the request.",
        )
        role_id = UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6")
        with pytest.raises(HTTPException) as exc_info:
            get_role_by_id(
                role_id=role_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Role not found" in exc_info.value.detail

    def test_get_role_by_id_roles_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="Couldn't process the request.",
        )
        role_id = UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6")
        with pytest.raises(HTTPException) as exc_info:
            get_role_by_id(
                role_id=role_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process your request." in exc_info.value.detail

    def test_get_role_by_id_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )
        role_id = UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6")
        with pytest.raises(HTTPException) as exc_info:
            get_role_by_id(
                role_id=role_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail

    def test_get_role_success(self, mock_actor, mock_iam):
        page = 0
        page_size = 10
        response = get_realm_role(
            page=page,
            page_size=page_size,
            iam=mock_iam,
            actor=mock_actor,
        )
        actual_schema = schemas.RolesList(result=ROLES_LIST)
        assert response == actual_schema

    def test_get_role_success_client_admin(self, mock_actor, mock_iam):
        mock_actor.realm_access.roles = [
            "create-realm",
            "default-roles-master",
            "offline_access",
            "admin",
            "ClientAdmin",
            "uma_authorization",
            "ClientUser",
        ]
        page = 0
        page_size = 10
        response = get_realm_role(
            page=page,
            page_size=page_size,
            actor=mock_actor,
            iam=mock_iam,
            user_info=mock_actor,
        )
        actual_schema = schemas.RolesList(result=ROLES_LIST)
        assert response == actual_schema

    def test_get_role_roles_not_found(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=RoleNotFound,
            message="Roles not found.",
        )
        page = 0
        page_size = 10
        with pytest.raises(HTTPException) as exc_info:
            get_realm_role(
                page=page,
                page_size=page_size,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Role not found." in exc_info.value.detail

    def test_get_role_roles_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We couldn't process your request.",
        )
        page = 0
        page_size = 10
        with pytest.raises(HTTPException) as exc_info:
            get_realm_role(
                page=page,
                page_size=page_size,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process your request." in exc_info.value.detail

    def test_get_role_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )
        page = 0
        page_size = 10
        with pytest.raises(HTTPException) as exc_info:
            get_realm_role(
                page=page,
                page_size=page_size,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail

    def test_get_role_max_number_invalid(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=MaxNumberInvalid,
            message="max Number should be a multiple of 10",
        )
        page = 0
        page_size = 5
        with pytest.raises(HTTPException) as exc_info:
            get_realm_role(
                page=page,
                page_size=page_size,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "max Number should be a multiple of 10" in exc_info.value.detail

    def test_delete_role_by_id_success(self, mock_actor, mock_iam):
        role_id = UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6")
        response = delete_role_by_id(
            role_id=role_id,
            iam=mock_iam,
            actor=mock_actor,
        )
        actual_response = True
        assert response == actual_response

    def test_delete_role_by_id_roles_not_found(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=RoleNotFound,
            message="Couldn't process the request.",
        )
        role_id = UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6")
        with pytest.raises(HTTPException) as exc_info:
            delete_role_by_id(
                role_id=role_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Role not found" in exc_info.value.detail

    def test_delete_role_by_id_roles_value_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=ValueError,
            message="We couldn't process your request.",
        )
        role_id = UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6")
        with pytest.raises(HTTPException) as exc_info:
            delete_role_by_id(
                role_id=role_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process your request." in exc_info.value.detail

    def test_delete_role_by_id_exception(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )
        role_id = UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6")
        with pytest.raises(HTTPException) as exc_info:
            delete_role_by_id(
                role_id=role_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Couldn't process the request." in exc_info.value.detail

    def test_delete_role_by_id_default_role_error(self, mock_actor, mock_iam_error):
        mock_iam_data = mock_iam_error(
            error_type=DefaultRoleError,
            message="Default role delete operation not allowed.",
        )
        role_id = UUID4("3fa85f64-5717-4562-b3fc-2c963f66afa6")
        with pytest.raises(HTTPException) as exc_info:
            delete_role_by_id(
                role_id=role_id,
                iam=mock_iam_data,
                actor=mock_actor,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Default role delete operation not allowed." in exc_info.value.detail
