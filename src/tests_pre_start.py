import logging
import os

from tenacity import after_log, before_log, retry, stop_after_attempt, wait_fixed

from tests.utils.keycloak import get_access_token

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

MAX_TRIES = 60 * 5  # 5 minutes
WAIT_SECONDS = 1


@retry(
    stop=stop_after_attempt(MAX_TRIES),
    wait=wait_fixed(WAIT_SECONDS),
    before=before_log(logger, logging.INFO),
    after=after_log(logger, logging.WARN),
)
def init() -> None:
    try:
        admin_username = os.environ.get("TEST_ADMIN_USERNAME", "admin")
        admin_password = os.environ.get("TEST_ADMIN_PASSWORD", "admin")
        get_access_token(admin_username, admin_password)
    except Exception as e:
        logger.error(e)
        raise e


def main() -> None:
    logger.info("Initializing service")
    init()
    logger.info("Service finished initializing")


if __name__ == "__main__":
    main()
