import logging
import uuid
from collections import defaultdict
from collections.abc import Generator
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from contextlib import contextmanager
from itertools import chain

from fastapi.encoders import jsonable_encoder
from keycloak_client import KeycloakAdmin, KeycloakException
from pydantic import UUID4, parse_obj_as
from starlette import status

from app.adapters.iam.base import (
    AbstractClaimPermissionAPI,
    AbstractClientAPI,
    AbstractGroupAPI,
    AbstractIAM,
    AbstractResourceAPI,
    AbstractRolesAPI,
    AbstractUsersAPI,
)
from app.adapters.iam.exc import (
    AlreadyExist,
    DefaultRoleError,
    GroupNotFound,
    InvalidScopeException,
    MemberNotFound,
    PermissionNotFound,
    PolicyAlreadyExist,
    PolicyNotFound,
    ResourceAlreadyExist,
    ResourceNotFound,
    ResourceScopePermissionNotFound,
    RoleAlreadyExist,
    RoleNotFound,
    ScopeAlreadyExist,
    ScopeNotFound,
    UsersNotFound,
)
from app.adapters.iam.schemas import (
    AssignGroupRoles,
    AuthorizedResponse,
    AuthorizedScopeRequest,
    ClaimPermission,
    ClaimPermissionResponse,
    ClientRole,
    GroupMember,
    GroupMemberResponse,
    GroupRoles,
    Groups,
    GroupsResponse,
    GroupSubgroup,
    Permission,
    PermissionDetails,
    PermissionScope,
    Policy,
    PolicyDetailsResponse,
    PolicyList,
    PolicyResponse,
    Resource,
    ResourceResponse,
    Role,
    RoleBaseResponse,
    RoleDetail,
    RolePolicyRequest,
    RolePolicyResponse,
    RoleRequest,
    RoleResponse,
    RolesList,
    ScopeList,
    Scopes,
    ScopeSchema,
    UpdatePermissionRequest,
    UpdatePermissionRoleRequest,
    UpdateResource,
    UpdateResourceSchema,
    UserScope,
    UserScopeRequest,
)
from app.api.decorators import measure_execution_time
from app.core.config import settings
from app.schemas import TokenResponse
from app.typedefs import RoleIgnore

logger = logging.getLogger(__name__)


class BaseKeycloakAPI:
    def __init__(self, ka: KeycloakAdmin):
        self.ka = ka


class KeycloakGroupAPI(BaseKeycloakAPI, AbstractGroupAPI):
    def _get_token_request_data(self) -> dict:
        request = {
            "grant_type": "password",
            "username": settings.KC_SVC_USER_NAME,
            "password": settings.KC_SVC_USER_PASSWORD,
            "client_id": settings.OIDC_CLIENT_ID,
            "client_secret": settings.OIDC_CLIENT_SECRET,
        }
        return request

    def _get_access_token(self) -> str:
        request = self._get_token_request_data()
        token_response = self.ka.users.get_token(
            oidc_token_url=f"{settings.OIDC_TOKEN_URL}", payload=request
        )
        self.access_token = token_response["access_token"]
        return self.access_token

    def _get_version(self) -> str:
        access_token = self._get_access_token()
        self.version = self.ka.groups.get_version(access_token)
        return self.version

    def get_subgroup_by_group(
        self,
        group_id: UUID4,
        page: int | None = None,
        page_size: int | None = None,
    ) -> list[GroupSubgroup]:
        version = self._get_version()
        logger.info("Getting group_by_parent_path.")
        with wrap_not_found_exception(GroupNotFound("Group not found.")):
            groups = self.ka.groups.get_subgroup(
                group_id=str(group_id), first=page, max_=page_size, version=version
            )

        logger.info("group_by_parent_path received.")
        return parse_obj_as(list[GroupSubgroup], groups)

    def get_group_members(
        self,
        group_id: UUID4,
        page: int | None = None,
        page_size: int | None = None,
    ) -> GroupMemberResponse:
        logger.info("Getting group_members.")
        with wrap_not_found_exception(MemberNotFound("Group members not found.")):
            members_data = self.ka.groups.get_members(
                group_id=str(group_id), first=page, max_=page_size
            )
            members: list[GroupMember] = []
            for member_ in members_data:
                member = GroupMember(**member_)
                roles = self.ka.users.get_realm_roles(member.id)
                member.roles = [RoleBaseResponse(**role) for role in roles]
                members.append(member)
        if not members:
            raise MemberNotFound("Group members not found.")
        return GroupMemberResponse(result=members)

    def groups_list(self) -> GroupsResponse:
        logger.info("Getting groups_list.")
        with wrap_not_found_exception(GroupNotFound("Groups not found.")):
            groups = self.ka.groups.get_many(brief_representation=False)
            group_data = [
                Groups(
                    id=group["id"],
                    name=group["name"],
                    path=group["path"],
                    # Not Applicable for the newer version
                    # subGroups=[Groups(**subgroup) for subgroup in group["subGroups"]],
                )
                for group in groups
            ]
        logger.info("Groups_list received.")
        return GroupsResponse(result=group_data)

    def get_group_roles(self, group_id: UUID4) -> RolesList:
        with wrap_not_found_exception(RoleNotFound()):
            group_roles_response = self.ka.groups.get_group_roles(
                group_id=str(group_id),
            )
        if not group_roles_response:
            raise RoleNotFound("Group roles not found.")
        default_roles = {
            "ClientAdmin",
            "ClientUser",
        }
        role_list = [
            self.ka.realm_roles.get_by_name(role_name) for role_name in default_roles
        ]
        default_role_names = set(default_roles)
        filtered_roles = list(
            chain(
                filter(
                    lambda role: role["name"] not in default_role_names,
                    group_roles_response,
                ),
                filter(lambda role: role["name"] in default_role_names, role_list),
            )
        )

        group_roles_data_response = RolesList(result=filtered_roles)

        for group_role in group_roles_data_response.result:  # type: ignore
            with wrap_not_found_exception(
                RoleNotFound(f"Role with name {str(group_role.name)} not found.")
            ):
                group_role_wise_user_list = (
                    self.ka.groups.get_group_role_wise_user_list(
                        role_name=group_role.name
                    )
                )
            group_role.userCount = len(group_role_wise_user_list)

        return group_roles_data_response

    def assign_group_roles(
        self, group_id: UUID4, group_roles: AssignGroupRoles
    ) -> RolesList:
        role_data = self.ka.realm_roles.get_many()
        if not role_data:
            raise RoleNotFound()
        role_data_response = RolesList(result=role_data)
        role_data_dict = dict(
            map(
                lambda role: (role.id, role.name),
                role_data_response.result,  # type: ignore
            )
        )
        updated_roles_name = list(
            map(
                lambda group_role: GroupRoles(
                    id=group_role.id,
                    name=role_data_dict.get(group_role.id, group_role.name),
                ),
                group_roles.roles,
            )
        )

        roles_not_found = list(filter(lambda role: not role.name, updated_roles_name))
        if roles_not_found:
            not_found_ids = [str(role.id) for role in roles_not_found]
            raise RoleNotFound(f"Role Not found with ids: [{', '.join(not_found_ids)}]")

        with wrap_not_found_exception(GroupNotFound(f"{group_id}")):
            self.ka.groups.assign_group_roles(
                group_id=str(group_id), payload=jsonable_encoder(updated_roles_name)
            )

        assign_group_roles_response = self.get_group_roles(group_id=group_id)
        return assign_group_roles_response

    def assign_roles_to_subgroups(
        self,
        group_list: list[Groups],
        group_one: list[Groups],
        group_role: AssignGroupRoles,
    ) -> bool:
        group_one_ids = {group.id for group in group_one}
        for subgroups in group_list:
            for subgroup in subgroups.subGroups:
                if subgroup.id in group_one_ids:
                    self.assign_group_roles(subgroup.id, group_role)
                    logger.info(f"Role {group_role} assigned to {subgroup.id}")
        return True


class KeycloakClient(BaseKeycloakAPI, AbstractClientAPI):
    def get_client_roles(self) -> list[ClientRole]:
        with wrap_not_found_exception(
            RoleNotFound("No roles found while getting client role.")
        ):
            result_data = self.ka.clients.get_client_roles(
                client_id=settings.KEYCLOAK_CLIENT_ID
            )
            roles = [ClientRole(**result) for result in result_data]
        if not roles:
            raise RoleNotFound("No roles found while getting client role.")
        return roles


class KeycloakRolesAPI(BaseKeycloakAPI, AbstractRolesAPI):
    @measure_execution_time
    def create_role_policy(self, role: RolePolicyRequest) -> RolePolicyResponse:

        role_list = self.get_roles_list()
        logger.info("**create_role_policy Roles received")
        policy_list = self.get_policy_list()
        logger.info("**create_role_policy Policies received")
        if role_list.result is not None:
            role_names = list(map(lambda r: r.name, role_list.result))
        else:
            role_names = []
        policy_names = list(map(lambda p: p.name, policy_list.result))

        if role.name in role_names and role.name in policy_names:
            raise AlreadyExist(
                f"Role and Policy with name '{str(role.name)}' Already exist"
            )
        logger.info("**create_role_policy Validating Roles and Policies received")
        if role.name in role_names and role.name not in policy_names:
            role_data_response = self.get_role_by_name(role_name=role.name)
            policy = self._create_policy_from_role_data(
                role_data_response=role_data_response
            )
            logger.info(
                "**create_role_policy Creating role as policy already "
                f"exist:- {role_data_response}"
            )
            response = RolePolicyResponse(
                **role_data_response.__dict__, policy_id=policy.id
            )
            return response

        with wrap_confilct_exception(RoleAlreadyExist()):
            role_data = Role(
                name=role.name, description=role.description, attributes=role.attributes
            )
            logger.info(f"**create_role_policy Create Role Request:- {role_data}")
            self.ka.realm_roles.create(jsonable_encoder(role_data))

        role_data_response = self.get_role_by_name(role_name=role.name)
        logger.info(f"**create_role_policy Created role:- {role_data_response}")
        policy = self._create_policy_from_role_data(
            role_data_response=role_data_response
        )
        logger.info(f"**create_role_policy Created policy:- {policy}")
        logger.info("**create_role_policy comming our of service layer")

        response = RolePolicyResponse(
            **role_data_response.__dict__, policy_id=policy.id
        )
        return response

    def _update_permission(
        self,
        policy_id: str,
        permission_id: str,
        scopes: list[ResourceResponse],
        unassign_policy: bool = False,
    ):
        policy_ids = [policy_id] if not unassign_policy else []
        permission_name, scope_id, resource_id, is_permission = None, None, None, False
        for scope_list in scopes:
            for scope in scope_list.scopes:
                for permission in scope.permission:
                    if str(permission.id) == permission_id:
                        permission_name, scope_id, resource_id, is_permission = (
                            permission.name,
                            str(scope.id),
                            str(scope_list.id),
                            True,
                        )
                        break
                if is_permission:
                    break
            if is_permission:
                break

        if not is_permission:
            raise PermissionNotFound(f"Permission not found with id: {permission_id}")
        permission_data = self.ka.claim_permission_admin.get(
            realm_client_id=settings.KEYCLOAK_CLIENT_ID,
            permission_id=permission_id,
        )
        policies = self.ka.realm_roles.get_policies(
            permission_id=permission_id, client_id=settings.KEYCLOAK_CLIENT_ID
        )
        if isinstance(policies, list):
            for policy in policies:
                policy_ids.append(policy["id"])
        else:
            policy_ids.append(policies["id"])
        if unassign_policy:
            policy_ids.remove(policy_id)
        update_request = UpdatePermissionRequest(
            id=permission_id,
            name=permission_name,
            description=permission_data["description"],
            resources=[resource_id],
            scopes=[scope_id],
            policies=policy_ids,
        )
        self.ka.realm_roles.update_role(
            permission_id=permission_id,
            payload=jsonable_encoder(update_request),
            client_id=settings.KEYCLOAK_CLIENT_ID,
        )

    # TODO - We need to update role_name, description
    # We are not updating role/policy - name, description in initial phase
    def update_role(
        self, role_id: UUID4, role: RoleRequest, scopes: list[ResourceResponse]
    ) -> bool:
        policy_list = self.get_policy_list()
        with wrap_not_found_exception(
            RoleNotFound(f"Role with name {str(role.name)} not found.")
        ):
            role_data = self.ka.realm_roles.get(role_id=str(role_id))

            roles_to_ignore = [
                RoleIgnore.DISTRIBUTORUSER.value,
                RoleIgnore.CLIENTUSER.value,
                RoleIgnore.DISTRIBUTORADMIN.value,
                RoleIgnore.CLIENTADMIN.value,
            ]
            role_details = RoleBaseResponse(**role_data)
            self._ignore_role_data(
                role_data=role_details, roles_to_ignore=roles_to_ignore
            )
            policy_id = None
            for policy in policy_list.result:
                if role_data["name"] == policy.name:
                    policy_id = policy.id
                    break
            if not policy_id:
                raise PolicyNotFound("Policy not found associated with this role")
        existing_permissions = (
            self.ka.claim_permission_admin.get_permission_by_policy_id(
                realm_client_id=settings.KEYCLOAK_CLIENT_ID, policy_id=str(policy_id)
            )
        )
        logger.info(f"Total permissions: {len(role.permission)}")
        with ThreadPoolExecutor() as executer:
            futures = [
                executer.submit(self._update_permission, policy.id, p, scopes)
                for p in role.permission
            ]
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"Thread pool error : {str(e)}")
                    raise

        if existing_permissions:
            permission_ids = []
            for permission in existing_permissions:
                permission_ids.append(permission["id"])

            removed_permissions = [
                permission_id
                for permission_id in permission_ids
                if permission_id not in role.permission
            ]

            with ThreadPoolExecutor() as executer:
                futures = [
                    executer.submit(
                        self._update_permission,
                        policy_id,
                        p,
                        scopes,
                        unassign_policy=True,
                    )
                    for p in removed_permissions
                ]
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        logger.error(f"Thread pool error : {str(e)}")
                        raise
        return True

    def _create_policy(self, policy: Policy) -> PolicyResponse:
        with wrap_confilct_exception(PolicyAlreadyExist()):
            create_policy = self.ka.policy.create(
                jsonable_encoder(policy), client_id=settings.KEYCLOAK_CLIENT_ID
            )
        return PolicyResponse(**create_policy)

    def get_roles_list(
        self,
        page: int | None = None,
        page_size: int | None = None,
        search: str | None = None,
    ) -> RolesList:
        role_data = self.ka.realm_roles.get_many(
            first=page, limit=page_size, search=search
        )
        if len(role_data) == 0:
            raise RoleNotFound("No roles found.")

        role_data_response = RolesList(result=role_data)
        role_details = self._filter_role_data(role_data=role_data_response)

        for role in role_details.result:  # type: ignore
            with wrap_not_found_exception(
                RoleNotFound(f"Role with name {str(role.name)} not found.")
            ):
                role_wise_user_list = self.ka.realm_roles.get_users_by_role_name(
                    role_name=role.name
                )
            role.userCount = len(role_wise_user_list)
        return role_details

    def get_group_roles(
        self,
        group_members: GroupMemberResponse,
        page: int | None = None,
        page_size: int | None = None,
        search: str | None = None,
    ) -> RolesList:
        role_data = self.ka.realm_roles.get_many(
            first=page, limit=page_size, search=search
        )
        if len(role_data) == 0:
            raise RoleNotFound("No roles found.")

        role_data_response = RolesList(result=role_data)
        role_count: dict[str, int] = defaultdict(int)

        for group_member in group_members.result:
            for role in group_member.roles or []:
                role_count[role.name] += 1
        role_list = RolesList()

        for group_member in group_members.result:
            for role in group_member.roles or []:
                if role_count[role.name] > 0:
                    role.userCount = role_count[role.name]
                    role_count[role.name] = 0  # Reset count to avoid duplication
                    role_list.result.append(role)

        for role_response in role_data_response.result or []:
            if role_response.name not in [r.name for r in role_list.result or []]:
                role_list.result.append(role_response)

        return self._filter_role_data(role_data=role_list)

    def get_policy_list(
        self, page: int | None = None, page_size: int | None = None
    ) -> PolicyList:
        policy_data = self.ka.policy.get_many(
            first=page, max_=page_size, client_id=settings.KEYCLOAK_CLIENT_ID
        )
        policy_data_list = policy_data
        if len(policy_data_list) == 0:
            raise PolicyNotFound()
        policy_data_response = PolicyList(result=policy_data_list)
        return policy_data_response

    def get_role_by_name(self, role_name: str) -> RoleResponse:
        role_data = self.ka.realm_roles.get_by_name(role_name)
        role_data_response = RoleResponse(**role_data)
        return role_data_response

    def _create_policy_from_role_data(
        self, role_data_response: RoleResponse
    ) -> PolicyResponse:
        role_ids = [RoleDetail(id=role_data_response.id)]
        policy = Policy(
            name=role_data_response.name,
            description=role_data_response.description,
            roles=role_ids,
        )
        policy_response = self._create_policy(policy=policy)
        return policy_response

    def get_role_by_id(self, role_id: UUID4) -> RoleResponse:
        with wrap_not_found_exception(
            RoleNotFound(f"Role not found with Id: {str(role_id)}")
        ):
            role_data = self.ka.realm_roles.get(role_id=str(role_id))
        role_data_response = RoleResponse(**role_data)
        return role_data_response

    def _ignore_role_data(
        self, role_data: RoleBaseResponse, roles_to_ignore: list[str]
    ) -> bool:
        if role_data.name in roles_to_ignore:
            raise DefaultRoleError(
                f"For default role '{role_data.name}' operation not allowed."
            )
        return True

    def _filter_role_data(self, role_data: RolesList) -> RolesList:
        roles_to_remove = [
            RoleIgnore.OFFLINE_ACCESS.value,
            RoleIgnore.UMA_AUTHORIZATION.value,
            RoleIgnore.APPLICATION.value,
            RoleIgnore.SAF_USER.value,
            RoleIgnore.DEFAULT_ROLES_STAGE_CONNECTED_PLATFORM.value,
            RoleIgnore.PARTNER_USER.value,
            RoleIgnore.DEFAULT_ROLES_DEV_CONNECTED_PLATFORM.value,
        ]
        filtered_data = list(
            filter(
                lambda item: item.name not in roles_to_remove,  # type: ignore
                role_data.result,  # type: ignore
            )
        )
        return RolesList(result=filtered_data)

    def delete_role_by_id(self, role_id: UUID4):
        with wrap_not_found_exception(
            RoleNotFound(f"Role not found while deleting with Id: {str(role_id)}")
        ):
            role = self.ka.realm_roles.get(role_id=str(role_id))
            roles_to_ignore = [
                RoleIgnore.DISTRIBUTORADMIN.value,
                RoleIgnore.DISTRIBUTORUSER.value,
                RoleIgnore.CLIENTADMIN.value,
                RoleIgnore.CLIENTUSER.value,
            ]
            role_details = RoleBaseResponse(**role)
            self._ignore_role_data(
                role_data=role_details, roles_to_ignore=roles_to_ignore
            )

        if role["attributes"]:
            if "false" in role["attributes"]["delete_role"]:
                raise DefaultRoleError("Default role cannot be deleted.")
        role_data = self.ka.realm_roles.delete(role_id=str(role_id))
        return role_data

    def get_all_permission(
        self, first: int | None = None, max_: int | None = None
    ) -> PermissionDetails:
        permissions = self.ka.claim_permission_admin.get_many(
            realm_client_id=settings.KEYCLOAK_CLIENT_ID,
            first=first,
            max_=max_ if max_ else 200,
        )
        permission_details = PermissionDetails(result=permissions)
        return permission_details

    @measure_execution_time
    def update_permissions_role(
        self,
        permission_role_data: UpdatePermissionRoleRequest,
        scopes: list[ResourceResponse],
    ) -> bool:
        policy_id = str(permission_role_data.policy_id)
        permission_id_list = permission_role_data.permission
        unassign_policy = permission_role_data.unassign_policy

        permission_list = self.get_all_permission()
        # Checking permissions to add into the role
        permission_ids = list(map(lambda ids: ids.id, permission_list.result))
        check_permission_ids = list(
            filter(
                lambda permission_details_id: permission_details_id
                not in permission_ids,
                permission_id_list,
            )
        )
        if check_permission_ids:
            raise PermissionNotFound(
                f"Permissions not found with ids: {check_permission_ids}"
            )

        policy_list = self.get_policy_list()
        logger.info("**update_permissions_role Policies received")
        policy_details = {policy.id: policy.name for policy in policy_list.result}

        valid_policy = policy_details.get(policy_id)
        logger.info(f"**update_permissions_role Requested policy:- {valid_policy}")
        if valid_policy is None:
            raise PolicyNotFound(f"Policy with ID: '{policy_id}' not found")

        with ThreadPoolExecutor() as executer:
            futures = [
                executer.submit(
                    self._update_permission, policy_id, p, scopes, unassign_policy
                )
                for p in permission_id_list
            ]
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"Thread pool error : {str(e)}")
                    raise
        logger.info("**update_permissions_role comming our of service layer")
        return True


class KeycloakPermissionsAPI(BaseKeycloakAPI, AbstractClaimPermissionAPI):
    def create_permission(
        self, claim_permission_admin: ClaimPermission
    ) -> ClaimPermissionResponse:
        premission_response = self.ka.claim_permission_admin.create(
            jsonable_encoder(claim_permission_admin),
            realm_client_id=settings.KEYCLOAK_CLIENT_ID,
        )
        return ClaimPermissionResponse(**premission_response)

    def get_permission(self, scope_id: UUID4) -> PermissionDetails:
        with wrap_not_found_exception(
            ScopeNotFound(
                f"Scope with ID: '{scope_id}' not found while \
                    'getting permission by scope'."
            )
        ):
            permission_data = self.ka.claim_permission_admin.get_permission_by_scope_id(
                realm_client_id=settings.KEYCLOAK_CLIENT_ID, scope_id=str(scope_id)
            )
        if permission_data:
            return PermissionDetails(result=permission_data)  # type: ignore
        else:
            raise PermissionNotFound(
                "Permission data not found for scope_id: {}".format(scope_id)
            )

    def get_permission_by_policy(self, policy_id: UUID4) -> PermissionDetails:
        with wrap_not_found_exception(
            PolicyNotFound(f"Policy with ID: '{policy_id}' not found")
        ):
            policy_permission_data = (
                self.ka.claim_permission_admin.get_permission_by_policy_id(
                    realm_client_id=settings.KEYCLOAK_CLIENT_ID,
                    policy_id=str(policy_id),
                )
            )
        policy_permission_data_response = policy_permission_data
        return PermissionDetails(result=policy_permission_data_response)


class KeycloakUsersAPI(BaseKeycloakAPI, AbstractUsersAPI):
    def get_evaluated_user_scope(self, token) -> TokenResponse:
        users_scope = self.ka.users_url.get_evaluated_user_scope(
            token, oidc_audience=settings.OIDC_AUDIENCE
        )
        return TokenResponse(**users_scope)

    def get_users_scopes(self, id: UUID4) -> list[UserScope]:
        users_scopes = self.users_scopes(id)["results"]
        return [
            UserScope(
                name=resource["resource"]["name"].split("with")[0].strip(),
                permission=[
                    Permission(
                        id=policy["policy"]["id"],
                        name=policy["policy"]["name"],
                        title=policy["policy"]["description"],
                    )
                    for policy in resource["policies"]
                    if policy["status"] == "PERMIT"
                ],
            )
            for resource in users_scopes
            if resource["status"] == "PERMIT"
        ]

    def users_scopes(self, id: UUID4):
        request_body = UserScopeRequest(
            clientId=settings.KEYCLOAK_CLIENT_ID,
            userId=id,
            entitlements=False,
        )
        evaluate_scope = self.ka.users.evaluate_scopes(
            payload=jsonable_encoder(request_body),
            client_id=settings.KEYCLOAK_CLIENT_ID,
        )
        return evaluate_scope

    def get_users(
        self, page: int | None = None, page_size: int | None = None
    ) -> GroupMemberResponse:
        users = self.ka.users.get_users(first=page, max_=page_size)
        if not users:
            raise UsersNotFound("No Users Found")
        users_info = GroupMemberResponse(result=users)
        for user in users_info.result:
            user_id = UUID4(user.id)
            user.roles = self.get_user_assigned_roles(user_id)
            user.groups = self.get_user_assigned_groups(user_id)
        return users_info

    def get_user_assigned_roles(self, id: UUID4) -> list[RoleBaseResponse]:
        user_roles = self.ka.users.get_user_roles(str(id))
        return [RoleBaseResponse(**user_role) for user_role in user_roles]

    def get_user_assigned_groups(self, id: UUID4) -> list[Groups]:
        user_groups = self.ka.users.get_user_groups(str(id))
        return [Groups(**group) for group in user_groups]

    def get_authorized_scope(
        self, auth_scope: Scopes, user_id: UUID4
    ) -> AuthorizedResponse:
        with wrap_internal_server_exception(
            InvalidScopeException(f"Invalid scope: {auth_scope.scope}")
        ):
            request_body = AuthorizedScopeRequest(
                resources=[{"scopes": [{"name": auth_scope.scope}]}],
                clientId=settings.KEYCLOAK_CLIENT_ID,
                userId=user_id,
            )
            evaluate_scope = self.ka.users.evaluate_scopes(
                jsonable_encoder(request_body),
                client_id=settings.KEYCLOAK_CLIENT_ID,
            )
        # matching_blocks = [
        #     policy
        #     for policy in evaluate_scope["results"][0]["policies"]
        #     if auth_scope.scope in policy["policy"]["scopes"]
        # ]
        result = (
            "PERMIT" if (evaluate_scope["results"][0]["status"] == "PERMIT") else "DENY"
        )
        return AuthorizedResponse(status=result)


class KeycloakResourcesAPI(BaseKeycloakAPI, AbstractResourceAPI):
    def check_resource_scope_by_name(
        self, resource: Resource | None = None, scopeList: ScopeList | None = None
    ) -> bool:
        get_resource = self.get_resource()
        resource_names = list(map(lambda r: r.name, get_resource))
        resource_scopes = list(map(lambda r: r.scopes, get_resource))

        resource_scope_names = []
        for scope in resource_scopes:
            for scope_data in scope:
                resource_scope_names.append(scope_data.name)
        if resource:
            current_resource_scopes_name = list(
                map(lambda r: r.name, resource.resource_scopes)
            )

            existing_scopes = list(
                filter(
                    lambda scope_name: scope_name in resource_scope_names,
                    current_resource_scopes_name,
                )
            )

            if resource.name in resource_names:
                raise ResourceAlreadyExist(
                    f"Resource with name {str(resource.name)} already exist."
                )
        if scopeList:
            current_resource_scopes_name = list(
                map(lambda r: r, scopeList.resource_scopes)
            )

            existing_scopes = list(
                filter(
                    lambda scope_name: scope_name in resource_scope_names,
                    current_resource_scopes_name,
                )
            )

        if existing_scopes:
            raise ScopeAlreadyExist(
                f"Scope with name {', '.join(existing_scopes)} already exists."
            )

        return True

    def create_resources(self, resource: Resource) -> ResourceResponse:
        resource.displayName = resource.name
        self.check_resource_scope_by_name(resource=resource)

        resource.owner = settings.KEYCLOAK_CLIENT_ID
        resource_data = self.ka.resources.create(jsonable_encoder(resource))
        return ResourceResponse(**resource_data)

    def update_resource(self, resource_id: UUID4, resource: ScopeList):
        with wrap_not_found_exception(
            ResourceNotFound(
                f"Resource not found while updating with Id: {str(resource_id)}"
            )
        ):

            self.check_resource_scope_by_name(scopeList=resource)
            server_scopes = self._get_server_scopes(resource_id)
            merged_scopes = self._merge_scopes(resource.resource_scopes, server_scopes)
            payload_data = self._create_payload(resource_id, merged_scopes)
            response = self._update_resource_payload(resource_id, payload_data)
        return response

    def _get_server_scopes(self, resource_id: UUID4) -> list[str]:
        result_data = self.ka.resources.get(resource_id=str(resource_id))
        server_scopes = [scope["name"] for scope in result_data["scopes"]]
        return server_scopes

    def _merge_scopes(
        self, user_scopes: list[str], server_scopes: list[str]
    ) -> list[str]:
        return list(set(user_scopes).union(server_scopes))

    def _create_payload(self, resource_id: UUID4, merged_scopes: list[str]):
        result_data = self._get_scopes_by_resource(resource_id)
        payload_data = UpdateResourceSchema(
            _id=resource_id, name=result_data.name, resource_scopes=merged_scopes
        )
        return jsonable_encoder(payload_data)

    def _get_scopes_by_resource(self, resource_id: UUID4) -> UpdateResource:
        json_data = self.ka.resources.get(resource_id=str(resource_id))
        return UpdateResource(**json_data)

    def _update_resource_payload(self, resource_id: UUID4, payload_data):
        return self.ka.resources.update(
            resource_id=str(resource_id), payload=payload_data
        )

    def get_resource(self) -> list[ResourceResponse]:
        result_data = self.ka.resources_admin.get_resources_by_client_id(
            client_id=settings.KEYCLOAK_CLIENT_ID
        )
        response_data = result_data
        if not response_data:
            raise ResourceNotFound("No resource data found.")
        return [ResourceResponse(**result) for result in response_data]

    def get_resource_by_id(self, resource_id: UUID4) -> ResourceResponse:
        with wrap_not_found_exception(
            ResourceNotFound(
                f"Resource not found while getting with Id: {str(resource_id)}"
            )
        ):
            result_data = self.ka.resources_admin.get_resource_by_id(
                resource_id=str(resource_id), client_id=settings.KEYCLOAK_CLIENT_ID
            )
        return ResourceResponse(**result_data)

    def get_resource_scopes_and_permissions(self) -> list[ResourceResponse]:
        resources = self.get_resource()
        for resource in resources:
            if resource.scopes:
                resource.scopes = self._get_permissions_by_scope(resource.scopes)
        if not resources:
            raise ResourceScopePermissionNotFound(
                "No resource, scope, permission data found while getting all together."
            )
        return resources

    def _get_permissions_by_scope(self, scopes: list[ScopeSchema]) -> list[ScopeSchema]:
        scope_with_permission = []
        for scope in scopes:
            permission_data = self._get_permissions(scope_id=uuid.UUID(scope.id))
            permissions = [
                Permission(
                    id=permission.id,
                    name=permission.name,
                    title=permission.name.replace("API", "").strip(),
                )
                for permission in permission_data
            ]
            scope_with_permission.append(
                ScopeSchema(
                    id=scope.id,
                    name=scope.name,
                    permission=permissions,
                )
            )
        return scope_with_permission

    def _get_permissions(self, scope_id: UUID4) -> list[PermissionScope]:
        result_data = self.ka.resources_admin.get_permissions_by_scope_id(
            scope_id=str(scope_id), client_id=settings.KEYCLOAK_CLIENT_ID
        )
        _permissions = list(map(lambda result: PermissionScope(**result), result_data))
        return _permissions

    def get_policies_by_permission(
        self, permission_id: UUID4
    ) -> list[PolicyDetailsResponse]:
        with wrap_not_found_exception(
            PermissionNotFound(
                f"Permission with id: {str(permission_id)} not found while \
                    getting policies by permission."
            )
        ):
            result_data = self.ka.resources_admin.get_policy_by_permission(
                permission_id=str(permission_id), client_id=settings.KEYCLOAK_CLIENT_ID
            )
        _policies = list(
            map(lambda result: PolicyDetailsResponse(**result), result_data)
        )
        if not _policies:
            raise PolicyNotFound(
                "No policies found for the requested permission with id:"
                f" {str(permission_id)}."
            )
        return _policies

    def delete_resource_by_id(self, resource_id: UUID4):
        with wrap_not_found_exception(
            ResourceNotFound(
                f"Resource not found while getting with Id: {str(resource_id)}"
            )
        ):
            get_resource_by_id = self.get_resource_by_id(resource_id=resource_id)
            scope_list = get_resource_by_id.scopes

        with wrap_not_found_exception(
            ResourceNotFound(
                f"Resource not found while deleting with Id: {str(resource_id)}"
            )
        ):
            resource_response = self.ka.resources.delete(str(resource_id))

        if resource_response and scope_list:
            for scope in scope_list:  # type: ignore
                with wrap_not_found_exception(
                    ScopeNotFound(
                        f"Scope not found while deleting with Id: {str(scope.id)}"
                    )
                ):
                    scope_response = self.ka.scopes_admin.delete(
                        scope_id=str(scope.id), client_id=settings.KEYCLOAK_CLIENT_ID
                    )
            return scope_response
        return resource_response


class KeycloakIAM(AbstractIAM):
    def __init__(self, ka: KeycloakAdmin):
        self.groups = KeycloakGroupAPI(ka)
        self.clients = KeycloakClient(ka)
        self.roles = KeycloakRolesAPI(ka)
        self.claim_permission_admin = KeycloakPermissionsAPI(ka)
        self.users = KeycloakUsersAPI(ka)
        self.users_admin = KeycloakUsersAPI(ka)
        self.resource = KeycloakResourcesAPI(ka)
        self.resources_admin = KeycloakResourcesAPI(ka)


class InMemoryKeycloakUsersAPI(AbstractUsersAPI):
    def get_evaluated_user_scope(self, token):
        return token

    def update_role(
        self, role_id: UUID4, role: RoleRequest, scopes: list[ResourceResponse]
    ) -> bool:
        pass

    def get_roles_list(
        self, page: int | None = None, page_size: int | None = None
    ) -> RolesList:
        pass

    def get_group_roles(
        self,
        group_members: GroupMemberResponse,
        page: int | None = None,
        page_size: int | None = None,
        search: str | None = None,
    ) -> RolesList:
        pass

    def get_users_scopes(self, id: UUID4) -> list[UserScope]:
        pass

    def get_users(
        self, page: int | None = None, page_size: int | None = None
    ) -> GroupMemberResponse:
        pass

    def get_user_assigned_roles(self, id: UUID4) -> list[RoleBaseResponse]:
        pass

    def get_user_assigned_groups(self, id: UUID4) -> list[Groups]:
        pass

    def get_authorized_scope(
        self, auth_scope: Scopes, user_id: UUID4
    ) -> AuthorizedResponse:
        pass

    def create_role_policy(self, role: RolePolicyRequest) -> RolePolicyResponse:
        pass

    def update_permissions_role(
        self,
        permission_role_data: UpdatePermissionRoleRequest,
        scopes: list[ResourceResponse],
        unassign_policy: bool = False,
    ) -> bool:
        pass


class FakeKeycloakClient(AbstractClientAPI):
    def get_client_roles(self) -> list[ClientRole]:
        result_data = [
            {
                "id": "340bd034-06af-485b-befc-1b1517ea896b",
                "name": "Test_spog_role",
                "description": "Test_spog_role",
                "composite": False,
                "clientRole": True,
                "containerId": "8c06012b-28ef-4a60-b81a-09d46802a776",
            },
            {
                "id": "d7c90d06-eae8-4cd1-8f2b-88505b5b50b9",
                "name": "uma_protection",
                "composite": False,
                "clientRole": True,
                "containerId": "8c06012b-28ef-4a60-b81a-09d46802a776",
            },
        ]
        roles = [ClientRole(**result) for result in result_data]
        return roles


@contextmanager
def wrap_not_found_exception(exc: Exception) -> Generator[None, None, None]:
    try:
        yield
    except KeycloakException as e:
        if e.response.status_code == status.HTTP_404_NOT_FOUND:
            raise exc
        raise e


@contextmanager
def wrap_confilct_exception(exc: Exception) -> Generator[None, None, None]:
    try:
        yield
    except KeycloakException as e:
        if e.response.status_code == status.HTTP_409_CONFLICT:
            raise exc
        raise e


@contextmanager
def wrap_internal_server_exception(exc: Exception) -> Generator[None, None, None]:
    try:
        yield
    except KeycloakException as e:
        if e.response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR:
            raise exc
        if e.response.status_code == status.HTTP_404_NOT_FOUND:
            raise exc
        raise e
