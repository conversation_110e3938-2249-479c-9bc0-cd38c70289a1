class IAMException(Exception):
    ...


class GroupNotFound(IAMException):
    ...


class MemberNotFound(IAMException):
    ...


class UnauthorizedUser(IAMException):
    ...


class ResourceNotFound(IAMException):
    ...


class RoleNotFound(IAMException):
    ...


class PermissionNotFound(IAMException):
    ...


class PolicyAlreadyExist(IAMException):
    ...


class RoleAlreadyExist(IAMException):
    ...


class PolicyNotFound(IAMException):
    ...


class AlreadyExist(IAMException):
    ...


class MaxNumberInvalid(IAMException):
    ...


class UsersNotFound(IAMException):
    ...


class ScopeNotFound(IAMException):
    ...


class DefaultRoleError(IAMException):
    ...


class ResourceScopePermissionNotFound(IAMException):
    ...


class ResourceAlreadyExist(IAMException):
    ...


class ScopeAlreadyExist(IAMException):
    ...


class InvalidScopeException(IAMException):
    ...


class ForbiddenError(IAMException):
    ...
