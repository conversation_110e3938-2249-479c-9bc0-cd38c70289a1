from abc import ABC, abstractmethod
from typing import Protocol

from pydantic import UUID4

from app.adapters.iam.schemas import (
    AssignGroupRoles,
    AuthorizedResponse,
    ClaimPermission,
    ClaimPermissionResponse,
    ClientRole,
    GroupMemberResponse,
    Groups,
    GroupsResponse,
    GroupSubgroup,
    PermissionDetails,
    PolicyDetailsResponse,
    PolicyList,
    Resource,
    ResourceResponse,
    RoleBaseResponse,
    RolePolicyRequest,
    RolePolicyResponse,
    RoleRequest,
    RoleResponse,
    RolesList,
    ScopeList,
    Scopes,
    UpdatePermissionRoleRequest,
    UserScope,
)


class AbstractGroupAPI(ABC):
    @abstractmethod
    def get_subgroup_by_group(
        self,
        group_id: UUID4,
        page: int | None = None,
        page_size: int | None = None,
    ) -> list[GroupSubgroup]:
        ...

    @abstractmethod
    def get_group_members(
        self,
        group_id: UUID4,
        page: int | None = None,
        page_size: int | None = None,
    ) -> GroupMemberResponse:
        ...

    @abstractmethod
    def groups_list(self) -> GroupsResponse:
        ...

    @abstractmethod
    def get_group_roles(self, group_id: UUID4) -> RolesList:
        ...

    @abstractmethod
    def assign_group_roles(
        self, group_id: UUID4, group_roles: AssignGroupRoles
    ) -> RolesList:
        ...

    @abstractmethod
    def assign_roles_to_subgroups(
        self,
        group_list: list[Groups],
        group_one: list[Groups],
        group_role: AssignGroupRoles,
    ) -> bool:
        ...


class AbstractUsersAPI(ABC):
    @abstractmethod
    def get_evaluated_user_scope(self, token):
        ...

    @abstractmethod
    def get_users_scopes(self, id: UUID4) -> list[UserScope]:
        ...

    def get_users(
        self, page: int | None = None, page_size: int | None = None
    ) -> GroupMemberResponse:
        ...

    def get_user_assigned_roles(self, id: UUID4) -> list[RoleBaseResponse]:
        ...

    def get_user_assigned_groups(self, id: UUID4) -> list[Groups]:
        ...

    @abstractmethod
    def get_authorized_scope(
        self, auth_scope: Scopes, user_id: UUID4
    ) -> AuthorizedResponse:
        ...


class AbstractResourceAPI(ABC):
    @abstractmethod
    def create_resources(self, resource: Resource) -> ResourceResponse:
        ...

    @abstractmethod
    def update_resource(self, resource_id: UUID4, resource: ScopeList):
        ...

    @abstractmethod
    def get_resource(self) -> list[ResourceResponse]:
        ...

    @abstractmethod
    def get_resource_by_id(self, resource_id: UUID4) -> ResourceResponse:
        ...

    @abstractmethod
    def get_resource_scopes_and_permissions(self) -> list[ResourceResponse]:
        ...

    @abstractmethod
    def get_policies_by_permission(
        self, permission_id: UUID4
    ) -> list[PolicyDetailsResponse]:
        ...

    @abstractmethod
    def delete_resource_by_id(self, resource_id: UUID4):
        ...


class AbstractClaimPermissionAPI(ABC):
    @abstractmethod
    def create_permission(
        self, claim_permission_admin: ClaimPermission
    ) -> ClaimPermissionResponse:
        ...

    @abstractmethod
    def get_permission(self, scope_id: UUID4) -> PermissionDetails:
        ...

    @abstractmethod
    def get_permission_by_policy(self, policy_id: UUID4) -> PermissionDetails:
        ...


class AbstractClientAPI(ABC):
    @abstractmethod
    def get_client_roles(self) -> list[ClientRole]:
        ...


class AbstractRolesAPI(ABC):
    @abstractmethod
    def update_role(
        self, role_id: UUID4, role: RoleRequest, scopes: list[ResourceResponse]
    ) -> bool:
        ...

    @abstractmethod
    def get_roles_list(
        self,
        page: int | None = None,
        page_size: int | None = None,
        search: str | None = None,
    ) -> RolesList:
        ...

    @abstractmethod
    def get_group_roles(
        self,
        group_members: GroupMemberResponse,
        page: int | None = None,
        page_size: int | None = None,
        search: str | None = None,
    ) -> RolesList:
        ...

    @abstractmethod
    def get_policy_list(
        self, page: int | None = None, page_size: int | None = None
    ) -> PolicyList:
        ...

    @abstractmethod
    def get_role_by_id(self, role_id: UUID4) -> RoleResponse:
        ...

    @abstractmethod
    def delete_role_by_id(self, role_id: UUID4):
        ...

    @abstractmethod
    def create_role_policy(self, role: RolePolicyRequest) -> RolePolicyResponse:
        ...

    @abstractmethod
    def update_permissions_role(
        self,
        permission_role_data: UpdatePermissionRoleRequest,
        scopes: list[ResourceResponse],
        unassign_policy: bool = False,
    ) -> bool:
        ...


class AbstractIAM(Protocol):
    groups: AbstractGroupAPI
    clients: AbstractClientAPI
    users: AbstractUsersAPI
    users_admin: AbstractUsersAPI
    resource: AbstractResourceAPI
    resources_admin: AbstractResourceAPI
    claim_permission_admin: AbstractClaimPermissionAPI
    roles: AbstractRolesAPI
