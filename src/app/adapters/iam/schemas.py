import re
from typing import Any, Dict, List, Union
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field, validator

from app.typedefs import (
    DecisionStrategy,
    PermissionType,
    PolicyLogic,
    PolicyType,
    RoleGroup,
    Username,
)

# GROUP


class Group(BaseModel):
    name: str
    path: str
    attributes: dict[str, list[str]] = {}


class Groups(BaseModel):
    id: UUID
    name: str
    path: str
    subGroups: list["Groups"] = []


class AccessModel(BaseModel):
    view: bool
    viewMembers: bool
    manageMembers: bool
    manage: bool
    manageMembership: bool


class GroupSubgroup(BaseModel):
    id: UUID
    name: str
    path: str
    parentId: UUID
    subGroupCount: int
    access: AccessModel


class RoleBaseResponse(BaseModel):
    id: UUID
    name: str
    description: str | None = None
    composite: bool
    clientRole: bool
    containerId: str
    userCount: int | None = 0
    attributes: dict[str, list[str]] = {}
    created_by: EmailStr | None


class GroupMember(BaseModel):
    id: str
    createdTimestamp: str
    username: Username
    enabled: bool
    totp: bool
    emailVerified: bool
    firstName: str | None = None
    lastName: str | None = None
    email: EmailStr
    attributes: dict[str, list[str]] | None = {}
    disableableCredentialTypes: dict[str, str] | None = {}
    requiredActions: list[str] | None = []
    notBefore: int
    roles: list[RoleBaseResponse] | None = []
    groups: list[Groups] | None = []


class GroupMemberResponse(BaseModel):
    result: list["GroupMember"]


class GroupRoles(BaseModel):
    id: UUID
    name: str | None = Field(readOnly=True)


class GroupRolesDetails(GroupRoles):
    path: str
    attributes: dict[str, Any] | None = {}
    realmRoles: list[str] | None = []
    clientRoles: dict[str, Union[str, list]] | None = {}
    subGroups: List["GroupRolesDetails"] | None = []


class GroupRolesResponse(GroupRolesDetails, BaseModel):
    access: dict[str, str] = {}


class AssignGroupRoles(BaseModel):
    roles: List["GroupRoles"] = Field(min_items=1)


# ROLE


class Role(BaseModel):
    name: str = Field(min_length=1, max_length=50)
    description: str | None = None
    attributes: dict[str, list[str]] = {}

    @validator("name")
    def validate_name(cls, value):
        # Check if the name contains only spaces
        if value.isspace():
            raise ValueError("Role name cannot consist of only spaces")

        # Check for special characters using regex
        if not re.match("^[a-zA-Z0-9_ -]*$", value):
            raise ValueError("Role name cannot contain special characters")
        return value.strip()


class RoleRequest(Role):
    roleGroup: RoleGroup
    permission: list[str] = Field(min_items=1)

    @validator("permission", each_item=True, pre=True, allow_reuse=True)
    def validate_resource_scopes(cls, value):
        # Check if the string contains only spaces
        if value.isspace():
            raise ValueError("Each item in permission cannot consist of only spaces")

        # Check for special characters using regex
        if not re.match("^[a-zA-Z0-9_ -]*$", value):
            raise ValueError(
                "Each item in permission cannot contain special characters"
            )

        # Check the length of each item
        if type(UUID(value)) is not UUID:
            raise ValueError("Each item in permission should be type UUID")

        # Check the length of each item
        if len(value) < 1:
            raise ValueError("Each item in permission cannot contain empty string")

        return value


class RolePolicyRequest(Role):
    roleGroup: RoleGroup


class RoleResponse(RoleBaseResponse):
    attributes: dict[str, list[str]] = {}


class RolePolicyResponse(RoleResponse):
    policy_id: UUID


class RolesList(BaseModel):
    result: list[RoleBaseResponse] = []


class ClientRole(BaseModel):
    id: UUID
    name: str
    description: str | None = None
    composite: bool
    clientRole: bool
    containerId: str


class ClientRoleList(BaseModel):
    result: list[ClientRole]


# SCOPE


class Scope(BaseModel):
    name: str
    displayName: str = Field(min_length=1, max_length=50)

    @validator("name")
    def validate_name(cls, value):
        # Check if the string contains only spaces
        if value.isspace():
            raise ValueError(
                "Each item in resource_scopes cannot consist of only spaces"
            )

        return value.strip()

    @validator("displayName")
    def validate_display_name(cls, value):
        # Check if the string contains only spaces
        if value.isspace():
            raise ValueError(
                "Each item in resource_scopes cannot consist of only spaces"
            )

        # Check for special characters using regex
        if not re.match("^[a-zA-Z0-9_ -]*$", value):
            raise ValueError(
                "Each item in resource_scopes cannot contain special characters"
            )

        # Check the length of each item
        if len(value) > 50:
            raise ValueError("Each item in resource_scopes cannot exceed 50 characters")

        # Check the length of each item
        if len(value) < 1:
            raise ValueError("Each item in resource_scopes cannot contain empty string")

        return value.strip()


# RESOURCE


class Resource(BaseModel):
    name: str = Field(min_length=1, max_length=50)
    displayName: str = Field(min_length=1, max_length=50, default=None, readOnly=True)
    resource_scopes: list[Scope] = Field(min_items=1)
    owner: str | None = Field(default=None, readOnly=True)

    @validator("name")
    def validate_name(cls, value):
        # Check if the name contains only spaces
        if value.isspace():
            raise ValueError("Name cannot consist of only spaces")

        # Check for special characters using regex
        if not re.match("^[a-zA-Z0-9_ -]*$", value):
            raise ValueError("Name cannot contain special characters")

        return value.strip()

    @validator("displayName")
    def validate_display_name(cls, value):
        # Check if the name contains only spaces
        if value.isspace():
            raise ValueError("Name cannot consist of only spaces")

        # Check for special characters using regex
        if not re.match("^[a-zA-Z0-9_ -]*$", value):
            raise ValueError("Name cannot contain special characters")

        return value.strip()


class CommonResponse(BaseModel):
    id: UUID
    name: str


class Permission(BaseModel):
    id: UUID
    name: str
    title: str


class ScopeSchema(BaseModel):
    id: str
    name: str | None = None
    permission: list[Permission] = []


class ResourceResponse(BaseModel):
    name: str
    owner: CommonResponse
    ownerManagedAccess: bool
    id: str = Field(alias="_id")
    displayName: str | None = None
    attributes: Dict[str, Any] | None = None
    uris: list[str] | None = []
    scopes: list[ScopeSchema] = []


class ResourceList(BaseModel):
    result: list[ResourceResponse]


class ScopeList(BaseModel):
    resource_scopes: List[str] = Field(min_items=1)

    @validator("resource_scopes", each_item=True, pre=True, allow_reuse=True)
    def validate_resource_scopes(cls, value):
        # Check if the string contains only spaces
        if value.isspace():
            raise ValueError(
                "Each item in resource_scopes cannot consist of only spaces"
            )

        # Check for special characters using regex
        if not re.match("^[a-zA-Z0-9_ -]*$", value):
            raise ValueError(
                "Each item in resource_scopes cannot contain special characters"
            )

        # Check the length of each item
        if len(value) > 50:
            raise ValueError("Each item in resource_scopes cannot exceed 50 characters")

        # Check the length of each item
        if len(value) < 1:
            raise ValueError("Each item in resource_scopes cannot contain empty string")

        return value.strip()


class UpdateResourceSchema(BaseModel):
    _id: UUID
    name: str
    resource_scopes: List[str]


class UpdateResource(BaseModel):
    name: str
    owner: dict
    ownerManagedAccess: bool
    attributes: dict
    _id: str
    uris: List[str]
    resource_scopes: List[dict]
    scopes: List[dict]


# PERMISSION


class ClaimPermission(BaseModel):
    name: str = Field(min_length=1, max_length=50)
    description: str | None = None
    permissionType: PermissionType = Field(
        alias="type", default=PermissionType.SCOPE, readOnly=True
    )
    policies: list[UUID] | None = []
    resources: list[UUID] | None = []
    scopes: list[str] = Field(min_items=1)
    logic: PolicyLogic = Field(default=PolicyLogic.POSITIVE, readOnly=True)
    decisionStrategy: DecisionStrategy = Field(
        default=DecisionStrategy.AFFIRMATIVE, readOnly=True
    )

    @validator("name")
    def validate_name(cls, value):
        if value.isspace():
            raise ValueError("Name cannot consist of only spaces")

        if not re.match("^[a-zA-Z0-9_ -]*$", value):
            raise ValueError("Name cannot contain special characters")

        return value.strip()

    @validator("scopes", each_item=True, pre=True, allow_reuse=True)
    def validate_resource_scopes(cls, value):
        # Check if the string contains only spaces
        if value.isspace():
            raise ValueError("Each string in scopes cannot consist of only spaces")

        if not value:
            raise ValueError("Each string in scopes should have at least one character")

        # Check for special characters using regex
        if not re.match("^[a-zA-Z0-9_ -]*$", value):
            raise ValueError("Each string in scopes cannot contain special characters")

        # Check the length of each item
        if len(value) > 50:
            raise ValueError("Each item in resource_scopes cannot exceed 50 characters")

        return value.strip()


class ClaimPermissionResponse(ClaimPermission):
    id: str


class GroupsResponse(BaseModel):
    result: list[Groups]


class PermissionScope(BaseModel):
    id: str
    name: str
    permission_type: str = Field(..., alias="type")
    logic: str
    decisionStrategy: str
    config: dict[str, str] = {}


class PermissionDetails(BaseModel):
    result: list[PermissionScope]


class UpdatePermissionRequest(BaseModel):
    id: UUID
    name: str
    description: str
    type: PermissionType = Field(
        alias="type", default=PermissionType.SCOPE, readOnly=True
    )
    logic: PolicyLogic = Field(default=PolicyLogic.POSITIVE, readOnly=True)
    decisionStrategy: DecisionStrategy = Field(
        default=DecisionStrategy.AFFIRMATIVE, readOnly=True
    )
    resources: list[str] = Field(min_items=1)
    scopes: list[str] = Field(min_items=1)
    policies: list[str] | None = []


# POLICIES


class RoleDetail(BaseModel):
    id: UUID


class Policy(BaseModel):
    name: str
    description: str | None = None
    policyType: PolicyType = Field(alias="type", default=PolicyType.ROLE, readOnly=True)
    logic: PolicyLogic = Field(default=PolicyLogic.POSITIVE, readOnly=True)
    decisionStrategy: DecisionStrategy = Field(
        default=DecisionStrategy.UNANIMOUS, readOnly=True
    )
    roles: list["RoleDetail"]


class PolicyResponse(Policy):
    id: str


class PolicyDetailsResponse(BaseModel):
    id: str
    name: str
    description: str | None = None
    policyType: str = Field(alias="type")
    logic: PolicyLogic
    decisionStrategy: DecisionStrategy = Field(default=DecisionStrategy.UNANIMOUS)
    config: dict[str, str] = {}


class PolicyList(BaseModel):
    result: list["PolicyDetailsResponse"]


# EVALUATE SCOPES


class UserScopeRequest(BaseModel):
    resources: list[str] | None = []
    context: dict[str, str] | None = {}
    roleIds: list[str] | None = []
    clientId: UUID
    userId: UUID
    entitlements: bool


class Scopes(BaseModel):
    scope: str = Field(min_length=1)

    class Config:
        schema_extra = {"example": {"scope": "View Account"}}


class AuthorizedResponse(BaseModel):
    status: str


class ScopeItem(BaseModel):
    name: str


class ResourceItem(BaseModel):
    scopes: List[ScopeItem]


class AuthorizedScopeRequest(BaseModel):
    resources: list[ResourceItem]
    context: dict[str, str] | None = Field(default={"attributes": {}}, readOnly=True)
    roleIds: list[str] | None = Field(default=[], readOnly=True)
    clientId: UUID = Field(readOnly=True)
    userId: UUID = Field(readOnly=True)
    entitlements: bool = Field(default=False, readOnly=True)


class UserScope(BaseModel):
    name: str
    permission: list[Permission] | None = []


class UserScopeList(BaseModel):
    result: list[UserScope]


class UpdatePermissionRoleRequest(BaseModel):
    policy_id: UUID
    permission: list[str] = Field(min_items=1)
    unassign_policy: bool = False

    @validator("permission", each_item=True, pre=True, allow_reuse=True)
    def validate_resource_scopes(cls, value):
        # Check if the string contains only spaces
        if value.isspace():
            raise ValueError("Each item in permission cannot consist of only spaces")

        # Check for special characters using regex
        if not re.match("^[a-zA-Z0-9_ -]*$", value):
            raise ValueError(
                "Each item in permission cannot contain special characters"
            )

        # Check the length of each item
        if type(UUID(value)) is not UUID:
            raise ValueError("Each item in permission should be type UUID")

        # Check the length of each item
        if len(value) < 1:
            raise ValueError("Each item in permission cannot contain empty string")

        return value
