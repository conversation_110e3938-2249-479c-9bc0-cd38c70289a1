import logging
from typing import Dict, Any

import httpx
from pydantic import BaseModel

from app.core.config import settings
from app.schemas.sim import SimDataSession

logger = logging.getLogger(__name__)


class WorldOVConfig(BaseModel):
    """Configuration for WorldOV API"""
    base_url: str
    access_token: str
    timeout: int


class WorldOVAPIException(Exception):
    """Custom exception for WorldOV API errors"""
    pass


class WorldOVAdapter:
    """Adapter for WorldOV API integration"""
    
    def __init__(self, config: WorldOVConfig = None):
        if config is None:
            # Use settings from configuration
            config = WorldOVConfig(
                base_url=settings.WORLDOV_API_BASE_URL,
                access_token=settings.WORLDOV_API_ACCESS_TOKEN,
                timeout=settings.WORLDOV_API_TIMEOUT
            )
        self.config = config
        self.headers = {
            "Authorization": f"Bearer {self.config.access_token}",
            "Content-Type": "application/json"
        }
    
    async def get_data_session_latest(self, iccid: str) -> SimDataSession:
        """
        Get the latest data session for a given ICCID
        
        Args:
            iccid: The ICCID of the SIM card
            
        Returns:
            SimDataSession: The latest data session information
            
        Raises:
            WorldOVAPIException: If the API call fails
        """
        url = f"{self.config.base_url}/data_sessions/{iccid}/latest"
        
        try:
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                logger.info(f"Making request to WorldOV API: {url}")
                response = await client.get(url, headers=self.headers)
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"Successfully retrieved data session for ICCID: {iccid}")
                    return SimDataSession(**data)
                elif response.status_code == 404:
                    logger.error(f"Data session not found for ICCID: {iccid}")
                    raise WorldOVAPIException(f"Data session not found for ICCID: {iccid}")
                elif response.status_code == 401:
                    logger.error("Unauthorized access to WorldOV API")
                    raise WorldOVAPIException("Unauthorized access to WorldOV API")
                else:
                    logger.error(f"WorldOV API error: {response.status_code} - {response.text}")
                    raise WorldOVAPIException(f"API error: {response.status_code}")
                    
        except httpx.TimeoutException:
            logger.error(f"Timeout while calling WorldOV API for ICCID: {iccid}")
            raise WorldOVAPIException("API request timeout")
        except httpx.RequestError as e:
            logger.error(f"Request error while calling WorldOV API: {str(e)}")
            raise WorldOVAPIException(f"Request error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error while calling WorldOV API: {str(e)}")
            raise WorldOVAPIException(f"Unexpected error: {str(e)}")


# Global instance with default configuration
worldov_adapter = WorldOVAdapter()