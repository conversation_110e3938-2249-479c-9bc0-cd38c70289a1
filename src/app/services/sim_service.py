import logging
from typing import Optional

from app.adapters.external_api.worldov import WorldOVAdapter, WorldOVAPIException
from app.schemas.sim import SimDataSession, SimDataSessionResponse

logger = logging.getLogger(__name__)


class SimService:
    """Service layer for SIM operations"""
    
    def __init__(self, worldov_adapter: WorldOVAdapter):
        self.worldov_adapter = worldov_adapter
    
    async def get_latest_data_session(self, iccid: str) -> SimDataSessionResponse:
        """
        Get the latest data session for a SIM card
        
        Args:
            iccid: The ICCID of the SIM card
            
        Returns:
            SimDataSessionResponse: Response containing the data session information
        """
        try:
            logger.info(f"Retrieving latest data session for ICCID: {iccid}")
            
            # Validate ICCID format (basic validation)
            if not iccid or len(iccid) < 15:
                logger.error(f"Invalid ICCID format: {iccid}")
                return SimDataSessionResponse(
                    data=None,
                    success=False,
                    message="Invalid ICCID format"
                )
            
            # Get data from WorldOV API
            data_session = await self.worldov_adapter.get_data_session_latest(iccid)
            
            logger.info(f"Successfully retrieved data session for ICCID: {iccid}")
            return SimDataSessionResponse(
                data=data_session,
                success=True,
                message="Data retrieved successfully"
            )
            
        except WorldOVAPIException as e:
            logger.error(f"WorldOV API error for ICCID {iccid}: {str(e)}")
            return SimDataSessionResponse(
                data=None,
                success=False,
                message=f"External API error: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error retrieving data session for ICCID {iccid}: {str(e)}")
            return SimDataSessionResponse(
                data=None,
                success=False,
                message="Internal server error"
            )