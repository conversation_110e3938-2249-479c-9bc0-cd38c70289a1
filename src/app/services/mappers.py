from typing import Optional

from app.adapters.iam.schemas import Groups, RoleBaseResponse
from app.schemas import User, UserInfo


def user_info_to_user(
    user_info: UserInfo,
    user_roles: list[RoleBaseResponse] | None = None,
    user_groups: Optional[list[Groups]] | None = None,
) -> User:
    return User(
        id=user_info.id,
        email=user_info.email,
        username=user_info.username,
        firstName=user_info.given_name,
        lastName=user_info.family_name,
        roles=user_roles,
        groups=user_groups,
    )
