from typing import Literal
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field

from app.adapters.iam.schemas import Group, GroupMember, RoleBaseResponse
from app.typedefs import Username


class RealmAccess(BaseModel):
    roles: list[str]


class UserOrganization(BaseModel):
    id: int
    type: str


class UserInfo(BaseModel):
    id: UUID = Field(alias="sub")
    email: EmailStr
    username: str
    email_verified: bool
    realm_access: RealmAccess
    organization: UserOrganization | None = None
    typ: Literal["Bearer"]
    given_name: str | None = None
    family_name: str | None = None
    name: str | None = None


class User(BaseModel):
    id: UUID
    username: Username
    email: EmailStr
    firstName: str | None = None
    lastName: str | None = None
    roles: list[RoleBaseResponse] | None = None
    groups: list[Group] | None = None


class TokenResponse(BaseModel):
    access_token: str


class UserDetails(GroupMember):
    attributes: dict[str, list[str]] | None = {}
    access: dict[str, str] = {}
