from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class SimDataSession(BaseModel):
    """Schema for SIM data session response from WorldOV API"""
    
    id: str
    partner_id: int
    iccid: str
    imsi: str
    start_time: datetime
    last_updated: datetime
    end_time: datetime
    ip_address: str
    apn_name: str
    bytes_total: int
    bytes_mo: int
    bytes_mt: int
    bytes_limit: int
    bytes_limit_threshold: int
    bytes_limit_used: int
    mobile_country_code: str
    mobile_network_code: str
    lac: str
    cellid: str


class SimDataSessionResponse(BaseModel):
    """Response wrapper for SIM data session"""
    
    data: Optional[SimDataSession] = None
    success: bool = True
    message: str = "Data retrieved successfully"