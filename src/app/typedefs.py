from enum import Enum
from typing import TypedDict

from pydantic import UUID4, ConstrainedStr


class UserDict(TypedDict):
    id: UUID4


class Username(ConstrainedStr):
    """Username field type based on Keycloak constraints."""

    min_length = 1
    max_length = 255


class DecisionStrategy(str, Enum):
    AFFIRMATIVE = "AFFIRMATIVE"
    CONSENSUS = "CONSENSUS"
    UNANIMOUS = "UNANIMOUS"


class PolicyLogic(str, Enum):
    NEGATIVE = "NEGATIVE"
    POSITIVE = "POSITIVE"


class PermissionType(str, Enum):
    RESOURCE = "resource"
    SCOPE = "scope"


class PolicyType(str, Enum):
    REGEX = "regex"
    ROLE = "role"
    CLIENT = "client"
    TIME = "time"
    USER = "user"
    CLIENT_SCOPE = "client scope"
    AGGREGATED = "aggregated"
    GROUP = "group"


class RoleGroup(str, Enum):
    MY_ORGANIZATION = "My Organization"
    ACCOUNT = "Account"


class RoleIgnore(str, Enum):
    APPLICATION = "Application"
    CLIENTADMIN = "ClientAdmin"
    CLIENTUSER = "ClientUser"
    DEFAULT_ROLES_DEV_CONNECTED_PLATFORM = "default-roles-dev-connected-platform"
    DEFAULT_ROLES_STAGE_CONNECTED_PLATFORM = "default-roles-stage-connected-platform"
    DISTRIBUTORADMIN = "DistributorAdmin"
    DISTRIBUTORUSER = "DistributorUser"
    OFFLINE_ACCESS = "offline_access"
    PARTNER_USER = "PartnerUser"
    SAF_USER = "SAF User"
    UMA_AUTHORIZATION = "uma_authorization"
