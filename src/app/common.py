from typing import Optional, <PERSON><PERSON>

import requests  # type: ignore

from app.adapters.iam.exc import MaxNumberInvalid
from app.core.config import settings


def get_page_index(
    first: int = None, max: int = None
) -> Tuple[Optional[int], Optional[int]]:

    if first is not None and max is not None:
        validate_max(max)
        if first > 0:
            page = (first - 1) * max
            return page, max
    return None, None


def validate_max(max_value: int) -> int:
    if max_value % 10 != 0:
        raise MaxNumberInvalid
    return max_value


def get_access_token() -> str:

    BASE_URL = f"{settings.BASE_URL}{settings.KEYCLOAK_REALM}"
    # Step 1: Token Generation.

    data = {
        "grant_type": "password",
        "username": f"{settings.OIDC_USER_NAME}",
        "password": f"{settings.OIDC_USER_PASWORD}",
        "client_id": f"{settings.OIDC_CLIENT_ID}",
        "client_secret": f"{settings.OIDC_CLIENT_SECRET}",
    }
    token_response = requests.post(
        f"{BASE_URL}/protocol/openid-connect/token",
        headers={"Content-Type": "application/x-www-form-urlencoded"},
        data=data,
        timeout=settings.TIMEOUT
    )
    token_response_data = token_response.json()

    # We need to use token for further curl request.
    access_token = token_response_data["access_token"]
    return access_token
