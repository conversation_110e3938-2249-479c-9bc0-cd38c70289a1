from collections.abc import Generator
from contextlib import contextmanager

import httpx
import keycloak_client

from app.adapters.iam.keycloak import KeycloakIAM
from app.core.config import settings


@contextmanager
def get_keycloak() -> Generator[keycloak_client.KeycloakAdmin, None, None]:
    with httpx.Client() as http:
        kc = keycloak_client.KeycloakAdmin(
            http,
            realm=settings.KEYCLOAK_REALM,
            keycloak_url=settings.KEYCLOAK_URL,
            client_id=settings.OIDC_CLIENT_ID,
            client_secret=settings.OIDC_CLIENT_SECRET,
        )
        yield kc


@contextmanager
def get_keycloak_iam() -> Generator[KeycloakIAM, None, None]:
    with get_keycloak() as ka:
        yield KeycloakIAM(ka)
