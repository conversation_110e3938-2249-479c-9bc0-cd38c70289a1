import json

from fastapi import <PERSON>TTP<PERSON>x<PERSON>
from keycloak_client import Key<PERSON>loakException


def http_exc_from_keycloak(e: KeycloakException) -> HTTPException:
    try:
        return HTTPException(
            status_code=e.response.status_code, detail=e.response.json()
        )
    except json.JSONDecodeError:
        return HTTPException(status_code=e.response.status_code, detail=str(e.args[0]))
