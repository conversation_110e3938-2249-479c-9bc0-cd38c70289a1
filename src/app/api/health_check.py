import os
import secrets

from fastapi import APIRouter, HTTPException, Request, Response, status

from app.core.config import settings

HEALTH_CHECK_SECRET_HEADER = os.getenv("HEALTH_CHECK_SECRET_HEADER", "Healthy-Header")


def validate_health_secret(request: Request) -> None:
    """
    Raises HTTPException(404) if valid health check secret is missing.
    """

    secret = request.headers.get(HEALTH_CHECK_SECRET_HEADER, "")
    # https://fastapi.tiangolo.com/advanced/security/http-basic-auth/?h=compare_digest#timing-attacks
    secret_is_valid = secrets.compare_digest(secret, settings.HEALTH_CHECK_SECRET)
    if not secret_is_valid:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)


router = APIRouter(
    default_response_class=Response,
    include_in_schema=True,
)


def ping() -> str:
    return "pong"


router.get("/authz/ping", response_model=str, name="ping")(ping)
router.get("/authz/healthy", response_model=str, name="healthy")(ping)
