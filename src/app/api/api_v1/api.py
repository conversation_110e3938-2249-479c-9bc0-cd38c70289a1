from fastapi import APIRouter

from app.api.api_v1.endpoints import (
    claims,
    client_roles,
    groups,
    policy,
    realm_roles,
    resources,
    users,
)

api_router = APIRouter(prefix="/authorization")
api_router.include_router(groups.router)
api_router.include_router(users.router)
api_router.include_router(resources.router)
api_router.include_router(claims.router)
api_router.include_router(realm_roles.router)
api_router.include_router(client_roles.router)
api_router.include_router(policy.router)
