import os

SCOPE_BASED_PERMISSION = {
    "name": "Test Permission",
    "description": "Test Permission",
    "type": "scope",
    "policies": ["6be0d4e3-d8d6-4957-afc0-c3b3e8f93767"],
    "resources": ["6531e95b-a046-4f9e-91c9-d0fe2c78bd95"],
    "scopes": ["62984cc7-6a84-4bd1-b571-c6445afeb7ec"],
    "logic": "POSITIVE",
    "decisionStrategy": "AFFIRMATIVE",
}

PERMISSION_SCOPE_RESPONSE = {
    "id": "20dbc8b9-726d-4685-ac75-7f0b1fcb7405",
    "name": "Test Permission",
    "description": "Test Permission",
    "type": "scope",
    "policies": ["6be0d4e3-d8d6-4957-afc0-c3b3e8f93767"],
    "resources": ["6531e95b-a046-4f9e-91c9-d0fe2c78bd95"],
    "scopes": ["62984cc7-6a84-4bd1-b571-c6445afeb7ec"],
    "logic": "POSITIVE",
    "decisionStrategy": "AFFIRMATIVE",
}

TOKEN = os.getenv(
    "TOKEN",
    "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJ6VzNPUWdzcTBfcWxDUH\
                  lYREgyYTVSbUJpZU1mUHc5R0psZjdGZDlWcDkwIn0.eyJleHAiOjE2OTYzMzcyMjEsImlhdCI6M\
                  TY5NjMzNzE2MSwiYXV0aF90aW1lIjoxNjk2MzM2NzA4LCJqdGkiOiIzZDJlODUyMS00OTY0LTRi\
                  ZTktOGJkYy0yZjQ3MjEwNWY2ODMiLCJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwODAvYXV0aC9\
                  yZWFsbXMvbWFzdGVyIiwiYXVkIjpbIm9yZ2FuaXphdGlvbi1tYW5hZ2VtZW50IiwiZGV2LXJlYW\
                  xtLXJlYWxtIiwibWFzdGVyLXJlYWxtIiwiYWNjb3VudCJdLCJzdWIiOiI5MGFiM2RhZS02ZGFjL\
                  TRlMWUtOTg2Zi0yNDc3MzI2YmYwM2EiLCJ0eXAiOiJCZWFyZXIiLCJhenAiOiJvcmdhbml6YXRp\
                  b24tbWFuYWdlbWVudCIsInNlc3Npb25fc3RhdGUiOiJhZDIzNzA5My1iNmQ3LTQxOWUtOGZkMC0\
                  2MDdhOGU5ZDgwNTYiLCJhY3IiOiIwIiwiYWxsb3dlZC1vcmlnaW5zIjpbImh0dHA6Ly9sb2NhbG\
                  hvc3Q6ODAwMCIsImh0dHA6Ly9sb2NhbGhvc3Q6OTAwMCJdLCJyZWFsbV9hY2Nlc3MiOnsicm9sZ\
                  XMiOlsiY3JlYXRlLXJlYWxtIiwiZGVmYXVsdC1yb2xlcy1tYXN0ZXIiLCJvZmZsaW5lX2FjY2Vz\
                  cyIsImFkbWluIiwiRGlzdHJpYnV0ZXJBZG1pbiIsIkRpc3RyaWJ1dG9yQWRtaW4iLCJ1bWFfYXV\
                  0aG9yaXphdGlvbiJdfSwicmVzb3VyY2VfYWNjZXNzIjp7ImRldi1yZWFsbS1yZWFsbSI6eyJyb2\
                  xlcyI6WyJ2aWV3LWlkZW50aXR5LXByb3ZpZGVycyIsInZpZXctcmVhbG0iLCJtYW5hZ2UtaWRlb\
                  nRpdHktcHJvdmlkZXJzIiwiaW1wZXJzb25hdGlvbiIsImNyZWF0ZS1jbGllbnQiLCJtYW5hZ2Ut\
                  dXNlcnMiLCJxdWVyeS1yZWFsbXMiLCJ2aWV3LWF1dGhvcml6YXRpb24iLCJxdWVyeS1jbGllbnR\
                  zIiwicXVlcnktdXNlcnMiLCJtYW5hZ2UtZXZlbnRzIiwibWFuYWdlLXJlYWxtIiwidmlldy1ldm\
                  VudHMiLCJ2aWV3LXVzZXJzIiwidmlldy1jbGllbnRzIiwibWFuYWdlLWF1dGhvcml6YXRpb24iL\
                  CJtYW5hZ2UtY2xpZW50cyIsInF1ZXJ5LWdyb3VwcyJdfSwibWFzdGVyLXJlYWxtIjp7InJvbGVz\
                  IjpbInZpZXctaWRlbnRpdHktcHJvdmlkZXJzIiwidmlldy1yZWFsbSIsIm1hbmFnZS1pZGVudGl\
                  0eS1wcm92aWRlcnMiLCJpbXBlcnNvbmF0aW9uIiwiY3JlYXRlLWNsaWVudCIsIm1hbmFnZS11c2\
                  VycyIsInF1ZXJ5LXJlYWxtcyIsInZpZXctYXV0aG9yaXphdGlvbiIsInF1ZXJ5LWNsaWVudHMiL\
                  CJxdWVyeS11c2VycyIsIm1hbmFnZS1ldmVudHMiLCJtYW5hZ2UtcmVhbG0iLCJ2aWV3LWV2ZW50\
                  cyIsInZpZXctdXNlcnMiLCJ2aWV3LWNsaWVudHMiLCJtYW5hZ2UtYXV0aG9yaXphdGlvbiIsIm1\
                  hbmFnZS1jbGllbnRzIiwicXVlcnktZ3JvdXBzIl19LCJhY2NvdW50Ijp7InJvbGVzIjpbIm1hbm\
                  FnZS1hY2NvdW50IiwibWFuYWdlLWFjY291bnQtbGlua3MiLCJ2aWV3LXByb2ZpbGUiXX0sIm9yZ\
                  2FuaXphdGlvbi1tYW5hZ2VtZW50Ijp7InJvbGVzIjpbInVtYV9wcm90ZWN0aW9uIl19fSwiYXV0\
                  aG9yaXphdGlvbiI6eyJwZXJtaXNzaW9ucyI6W3sic2NvcGVzIjpbImNyZWF0ZV9pbXNpcyJdLCJ\
                  yc2lkIjoiNmIzMjZjZTAtMTA2ZS00ODljLThiYWQtMzQyOGEzNGYxMDE0IiwicnNuYW1lIjoic2\
                  ltX21hbmFnZW1lbnQifSx7InNjb3BlcyI6WyJ2aWV3IHJhdGUgcGxhbiIsImNyZWF0ZS9tb2RpZ\
                  nkgcmF0ZXBsYW4iXSwicnNpZCI6IjZlOTc0NzUxLWVmNWUtNDExYS1iMGFhLTdhZjNhMjBhZDU5\
                  YiIsInJzbmFtZSI6InJhdGVfcGxhbnMifSx7InJzaWQiOiIxYWVhNDBhYS1iYTM1LTQ0MzQtODV\
                  lZS04MDc0NWZmNjZmNTIiLCJyc25hbWUiOiJEZWZhdWx0IFJlc291cmNlIn1dfSwic2NvcGUiOi\
                  JjcmVhdGUvbW9kaWZ5X3JhdGVwbGFuIHByb2ZpbGUgZW1haWwgc2ltX21hbmFnZW1lbnQiLCJza\
                  WQiOiJhZDIzNzA5My1iNmQ3LTQxOWUtOGZkMC02MDdhOGU5ZDgwNTYiLCJlbWFpbF92ZXJpZmll\
                  ZCI6dHJ1ZSwibmFtZSI6IkhhcHB5IEt1bWFyIFNoYXJtYSIsInByZWZlcnJlZF91c2VybmFtZSI\
                  6ImhhcHB5a3VtYXIuc2hhcm1hIiwiZ2l2ZW5fbmFtZSI6IkhhcHB5IEt1bWFyIiwiZmFtaWx5X2\
                  5hbWUiOiJTaGFybWEiLCJlbWFpbCI6ImhhcHB5a3VtYXIuc2hhcm1hQG5leHRnZW5jbGVhcmluZ\
                  y5jb20ifQ.fWCPiD83i65ZrB64mX7-9WPnRElVpUW05HSFB567JfYg3HqWiliooqx5SpqnNEfBW\
                  XtBMTLC44fhytsEBZdmsOyfsBDE8rge_R8pZqc-0t92hWgvSbjY4KYhMYt-plnMo5PGlkjrTmS7\
                  rBMrC_27iI3kcypkNPOccPWt7f_ASO162hLtBVHk8AwMXhqIZ0xTltIY6j2ZXPrGJOpUSQyteP1\
                  kWD6iAVhePcyAA50yWmO7aVHlDOKleYL2M8dPeBl5imXtxpafPIoON41Zn5ua91QpbHxGeVrnr_\
                  It-veKDu0YR__8L8ibD_CfN3_w4Gw5p7IKmfMm_BcVK2THZIkBLA",
)

CREATE_REALM_ROLES = {
    "name": "Test",
    "description": "Test",
    "attributes": {},
    "roleGroup": "My Organization",
}

UPDATE_REALM_ROLES = {
    **CREATE_REALM_ROLES,  # type: ignore
    "permission": ["********-e432-4395-a9ad-1cadfd5b8a9f"],
}
PERMISSION_LIST = [
    {
        "id": "0e83e310-10c7-47e9-9cb0-ced1137c74f9",
        "name": "Default Permission",
        "type": "resource",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {},
    },
    {
        "id": "5110adc5-04c9-4886-ace5-d8e0aa276543",
        "name": "admin",
        "type": "scope",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {},
    },
    {
        "id": "e6865c9c-a267-44be-9187-40c12ca77141",
        "name": "user",
        "type": "scope",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {},
    },
    {
        "id": "c945c2df-2528-4a6e-a931-34778a720415",
        "name": "distributor",
        "type": "scope",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {},
    },
    {
        "id": "2558be50-982c-46de-8a00-3005dbd5560c",
        "name": "update",
        "type": "scope",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {},
    },
]

PERMISSION_DATA = {
    "id": "e91a3cc5-19e4-4f3c-9cc8-412c619ac587",
    "name": "TestPermission",
    "description": "TestPermission",
    "type": "scope",
    "logic": "POSITIVE",
    "decisionStrategy": "AFFIRMATIVE",
}

ACTOR_DETAILS = {
    "sub": "b98565ba-1e50-4c12-8453-3439805971e6",
    "email": "<EMAIL>",
    "username": "admin",
    "email_verified": True,
    "organization": None,
    "typ": "Bearer",
    "given_name": "admin",
    "family_name": "admin",
    "name": "admin admin",
}

REALM_ROLES_RESPONSE = {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "name": "Test",
    "description": "Test",
    "composite": True,
    "clientRole": True,
    "containerId": "master",
    "userCount": 3,
    "attributes": {},
}

REALM_ROLES_POLICY_RESPONSE = {
    **REALM_ROLES_RESPONSE,  # type: ignore
    "policy_id": "4fa85f64-5887-4562-b3fc-8c963f66afa7",
}

ROLES_LIST = [
    {
        "id": "6de84279-e9f7-4524-bbca-ea3d727c37ae",
        "name": "ClinetAdmin",
        "description": "ClinetAdmin",
        "composite": False,
        "clientRole": False,
        "containerId": "master",
        "userCount": 3,
        "attributes": {},
    },
    {
        "id": "d64c8a9a-082d-4fa7-af11-62ede6de7ebc",
        "name": "ClientUser",
        "description": "ClientUser",
        "composite": False,
        "clientRole": False,
        "containerId": "master",
        "userCount": 3,
        "attributes": {},
    },
    {
        "id": "8767e6c1-5d55-4b05-a372-327c649249ab",
        "name": "DistributorAdmin",
        "description": "DistributorAdmin",
        "composite": False,
        "clientRole": False,
        "containerId": "master",
        "userCount": 3,
        "attributes": {},
    },
]

POLICIES_LIST = [
    {
        "id": "97d10628-1ba3-4a5e-9c62-50848930062e",
        "name": "ClientUser",
        "description": "ClientUser",
        "type": "role",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {
            "roles": '[{"id":"ca1a1972-f63b-486d-ae72-d177f832e1d4","required":false}]'
        },
    },
    {
        "id": "e6865c9c-a267-44be-9187-40c12ca77141",
        "name": "ClientAdin",
        "description": "ClientAdmin",
        "type": "scope",
        "logic": "POSITIVE",
        "decisionStrategy": "AFFIRMATIVE",
        "config": {},
    },
    {
        "id": "67c32726-f27b-4319-af0a-e143a68ab72a",
        "name": "DistributorAdmin",
        "description": "DistributorAdmin",
        "type": "scope",
        "logic": "POSITIVE",
        "decisionStrategy": "AFFIRMATIVE",
        "config": {},
    },
    {
        "id": "64805f86-5f9c-47c0-bbf1-748d3236a0ad",
        "name": "DistributorClient",
        "description": "DistributorClient",
        "type": "role",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {
            "roles": '[{"id":"04977a97-597a-4542-985d-17cbc325b329","required":false},\
                {"id":"ca1a1972-f63b-486d-ae72-d177f832e1d4","required":false}]'
        },
    },
    {
        "id": "5110adc5-04c9-4886-ace5-d8e0aa276543",
        "name": "User",
        "description": "User",
        "type": "scope",
        "logic": "POSITIVE",
        "decisionStrategy": "AFFIRMATIVE",
        "config": {},
    },
]

USER_INFO_RESPONSE = {
    "id": "b98565ba-1e50-4c12-8453-3439805971e6",
    "username": "admin",
    "email": "<EMAIL>",
    "firstName": "admin",
    "lastName": "admin",
    "roles": [
        {
            "id": "5d314d82-4ba3-462a-a2b0-cec69ad2fc14",
            "name": "default-roles-spog-local",
            "description": "${role_default-roles}",
            "composite": True,
            "clientRole": False,
            "containerId": "spog-local",
            "userCount": 1,
        }
    ],
    "groups": [
        {"name": "Nextgen Clearing", "path": "/Nextgen Clearing", "attributes": {}}
    ],
}

TOKEN_RESPONSE = {"access_token": TOKEN}

EVALUATE_SCOPE = {
    "name": "account_management",
    "permission": [
        {
            "id": "9cffe4ef-573a-4a03-8674-6b7d76265503",
            "name": "Create Account Permission",
            "title": "Create Account Permission",
        }
    ],
}

USER_DETAILS = [
    {
        "id": "b98565ba-1e50-4c12-8453-3439805971e6",
        "createdTimestamp": "*************",
        "username": "admin",
        "enabled": True,
        "totp": False,
        "emailVerified": True,
        "firstName": "admin",
        "lastName": "admin",
        "email": "<EMAIL>",
        "attributes": {"asd": ["asd"]},
        "disableableCredentialTypes": {},
        "requiredActions": [],
        "notBefore": 0,
    }
]

ASSIGN_GROUP_ROLES = [
    {
        "id": "d64c8a9a-082d-4fa7-af11-62ede6de7ebc",
    },
    {
        "id": "6de84279-e9f7-4524-bbca-ea3d727c37ae",
    },
]

GROUP_ROLES_LIST = [
    {
        "id": "d64c8a9a-082d-4fa7-af11-62ede6de7ebc",
        "name": "ClientUser",
        "description": "ClientUser",
        "composite": False,
        "clientRole": False,
        "containerId": "master",
        "userCount": 2,
    },
    {
        "id": "49f7c18f-f3e0-4750-be43-5702ee41e420",
        "name": "uma_authorization",
        "description": "uma_authorization",
        "composite": False,
        "clientRole": False,
        "containerId": "master",
        "userCount": 1,
    },
    {
        "id": "8767e6c1-5d55-4b05-a372-327c649249ab",
        "name": "DistributorAdmin",
        "description": "DistributorAdmin",
        "composite": False,
        "clientRole": False,
        "containerId": "master",
        "userCount": 3,
    },
    {
        "id": "6de84279-e9f7-4524-bbca-ea3d727c37ae",
        "name": "ClinetAdmin",
        "description": "ClinetAdmin",
        "composite": False,
        "clientRole": False,
        "containerId": "master",
        "userCount": 1,
    },
]


POLICY_PERMISSION = [
    {
        "id": "0e83e310-10c7-47e9-9cb0-ced1137c74f9",
        "name": "Default Permission",
        "type": "resource",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {},
    },
    {
        "id": "5110adc5-04c9-4886-ace5-d8e0aa276543",
        "name": "admin",
        "type": "scope",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {},
    },
    {
        "id": "e6865c9c-a267-44be-9187-40c12ca77141",
        "name": "user",
        "type": "scope",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {},
    },
    {
        "id": "c945c2df-2528-4a6e-a931-34778a720415",
        "name": "distributor",
        "type": "scope",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {},
    },
    {
        "id": "2558be50-982c-46de-8a00-3005dbd5560c",
        "name": "update",
        "type": "scope",
        "logic": "POSITIVE",
        "decisionStrategy": "UNANIMOUS",
        "config": {},
    },
]

SCOPE_PERMISSION = {
    "id": "5110adc5-04c9-4886-ace5-d8e0aa276543",
    "name": "update",
    "type": "scope",
    "logic": "POSITIVE",
    "decisionStrategy": "UNANIMOUS",
    "config": {},
}

SCOE_UUID_LIST = [
    "5110adc5-04c9-4886-ace5-d8e0aa276543",
    "5cd90288-036b-45e1-9532-a3cbbc96dbbd",
]

GROUP1 = {
    "id": "9eab741e-4b45-4143-9c1a-ff60782e4add",
    "name": "Nextgen Clearing",
    "path": "/Nextgen Clearing",
    "subGroups": [
        {
            "id": "cdc27aac-bff4-4541-96fb-902ecd16c300",
            "name": "bt_test_account",
            "path": "/Nextgen Clearing/bt_test_account",
            "subGroups": [],
        },
        {
            "id": "5cd90288-036b-45e1-9532-a3cbbc96dbbd",
            "name": "bt_sample_account",
            "path": "/Nextgen Clearing/bt_sample_account",
            "subGroups": [],
        },
        {
            "id": "69076b0d-8f64-43f6-8ee0-4d2937d61015",
            "name": "bt_push_cdr_account",
            "path": "/Nextgen Clearing/bt_push_cdr_account",
            "subGroups": [
                {
                    "id": "171fc2a4-dac3-4748-b656-03e889c5acc7",
                    "name": "bt_test_master",
                    "path": "/Nextgen Clearing/bt_push_cdr_account/bt_test_master",
                    "subGroups": [],
                }
            ],
        },
    ],
}

GROUP2 = {
    "id": "e83e64ff-71b7-42be-bc5a-8e8ad2968b0a",
    "name": "Nextgen Clearing1",
    "path": "/Nextgen Clearing1",
    "subGroups": [],
}

GROUPS = [GROUP1, GROUP2]


GROUP_BY_PARENT_PATH = {
    "id": "9eab741e-4b45-4143-9c1a-ff60782e4add",
    "name": "Nextgen Clearing",
    "path": "/Nextgen Clearing",
    "attributes": {"Test": ["Test"]},
    "realmRoles": ["DistributorAdmin", "ClinetAdmin", "admin"],
    "clientRoles": {},
    "subGroups": [
        {
            "id": "cdc27aac-bff4-4541-96fb-902ecd16c300",
            "name": "bt_test_account",
            "path": "/Nextgen Clearing/bt_test_account",
            "attributes": {},
            "realmRoles": ["DistributorAdmin", "ClinetAdmin"],
            "clientRoles": {},
            "subGroups": [],
        },
        {
            "id": "5cd90288-036b-45e1-9532-a3cbbc96dbbd",
            "name": "bt_sample_account",
            "path": "/Nextgen Clearing/bt_sample_account",
            "attributes": {},
            "realmRoles": [
                "DistributorAdmin",
                "ved",
                "ClinetAdmin",
                "HAPPY",
                "harshang",
                "ved-master",
            ],
            "clientRoles": {"organization-management": ["uma_protection"]},
            "subGroups": [],
        },
        {
            "id": "69076b0d-8f64-43f6-8ee0-4d2937d61015",
            "name": "bt_push_cdr_account",
            "path": "/Nextgen Clearing/bt_push_cdr_account",
            "attributes": {},
            "realmRoles": ["DistributorAdmin", "ClinetAdmin", "ClientUser"],
            "clientRoles": {},
            "subGroups": [
                {
                    "id": "171fc2a4-dac3-4748-b656-03e889c5acc7",
                    "name": "bt_test_master",
                    "path": "/Nextgen Clearing/bt_push_cdr_account/bt_test_master",
                    "attributes": {"Test_1": ["_Test"]},
                    "realmRoles": [
                        "DistributorAdmin",
                        "uma_authorization",
                        "default-roles-master",
                        "ClinetAdmin",
                        "ClientUser",
                    ],
                    "clientRoles": {"organization-management": ["uma_protection"]},
                    "subGroups": [],
                }
            ],
        },
    ],
}

GROUP_SUB_GROUP = {
    "id": "9eab741e-4b45-4143-9c1a-ff60782e4add",
    "name": "Nextgen Clearing",
    "path": "/Nextgen Clearing",
    "parentId": "6eab741e-4b32-4143-9c1a-ff60782e4add",
    "subGroupCount": 1,
    "access": {
        "view": True,
        "viewMembers": True,
        "manageMembers": True,
        "manage": True,
        "manageMembership": True,
    },
}


RESOURCE_LIST = {
    "results": [
        {
            "resource": {
                "name": "account_management",
                "_id": "4c8b83bc-468f-46c6-ab6d-a49f0677315e",
            },
            "scopes": [],
            "policies": [
                {
                    "policy": {
                        "id": "9cffe4ef-573a-4a03-8674-6b7d76265503",
                        "name": "Create Account Permission",
                        "description": "Create Account Permission",
                        "type": "resource",
                        "resources": [],
                        "scopes": [],
                        "logic": "POSITIVE",
                        "decisionStrategy": "UNANIMOUS",
                        "config": {},
                    },
                    "status": "PERMIT",
                    "associatedPolicies": [
                        {
                            "policy": {
                                "id": "f07a0f2e-6761-44a1-9ac2-ce59b4851875",
                                "name": "Default Policy",
                                "description": "A policy that grants access only \
                                    for users within this realm",
                                "type": "js",
                                "resources": [],
                                "scopes": [],
                                "logic": "POSITIVE",
                                "decisionStrategy": "AFFIRMATIVE",
                                "config": {},
                            },
                            "status": "PERMIT",
                            "associatedPolicies": [],
                            "scopes": [],
                        }
                    ],
                    "scopes": [],
                }
            ],
            "status": "PERMIT",
            "allowedScopes": [],
        },
    ],
    "entitlements": "FALSE",
    "status": "DENY",
    "rpt": {
        "exp": **********,
        "iat": **********,
        "jti": "4ec44833-ba33-4393-b206-39612ed2125c",
        "aud": ["organization-management", "master-realm", "account"],
        "sub": "b98565ba-1e50-4c12-8453-3439805971e6",
        "typ": "Bearer",
        "azp": "organization-management",
        "session_state": "54a81b51-1b42-48e0-8c8e-754c19aaa028",
        "acr": "1",
        "allowed-origins": ["http://localhost:8002"],
        "realm_access": {
            "roles": [
                "ClinetAdmin",
                "create-realm",
                "default-roles-master",
                "offline_access",
                "admin",
                "DistributorAdmin",
                "uma_authorization",
                "ClientUser",
            ]
        },
        "resource_access": {
            "master-realm": {
                "roles": [
                    "view-identity-providers",
                    "view-realm",
                    "manage-identity-providers",
                    "impersonation",
                    "create-client",
                    "manage-users",
                    "query-realms",
                    "view-authorization",
                    "query-clients",
                    "query-users",
                    "manage-events",
                    "manage-realm",
                    "view-events",
                    "view-users",
                    "view-clients",
                    "manage-authorization",
                    "manage-clients",
                    "query-groups",
                ]
            },
            "account": {
                "roles": ["manage-account", "manage-account-links", "view-profile"]
            },
            "organization-management": {"roles": ["uma_protection"]},
        },
        "authorization": {
            "permissions": [
                {
                    "rsid": "4c8b83bc-468f-46c6-ab6d-a49f0677315e",
                    "rsname": "Default Resource",
                },
                {
                    "scopes": ["sim_delete", "sim_view", "sim_create/modify"],
                    "rsid": "3da571ee-48da-46b7-9c18-6971a0cc2034",
                    "rsname": "SIMManagement",
                },
                {
                    "scopes": [
                        "rateplan_delete",
                        "rateplan_create/modify",
                        "rateplan_view",
                    ],
                    "rsid": "7b24a64d-e572-4bf1-af4a-3c9727ef950c",
                    "rsname": "RatePlan",
                },
                {
                    "scopes": [
                        "account_create/modify",
                        "account_delete",
                        "account_view",
                    ],
                    "rsid": "13846e8d-505f-4d92-a5d6-9426db3c1399",
                    "rsname": "AccountManagement",
                },
                {
                    "scopes": [
                        "billing_create/modify",
                        "billing_delete",
                        "billing_view",
                    ],
                    "rsid": "ec33dea8-c1a3-4c73-bd6e-0561b30ef945",
                    "rsname": "Billing",
                },
            ]
        },
        "scope": "email profile",
        "sid": "54a81b51-1b42-48e0-8c8e-754c19aaa028",
        "email_verified": "TRUE",
        "name": "admin admin",
        "preferred_username": "admin",
        "given_name": "admin",
        "family_name": "admin",
        "email": "<EMAIL>",
    },
}

CREATE_RESOURCE = {
    "name": "TestResource4",
    "resource_scopes": [
        {"name": "/v1/authorization/resource/", "displayName": "TestScope4"}
    ],
}


PERMISSION_SCOPE = {
    "id": "********-e432-4395-a9ad-1cadfd5b8a9f",
    "name": "account_create/modify",
    "title": "account_create/modify",
}

PERMISSION_BY_SCOPE_COMMON_RESPONSE = {
    "id": "********-6118-4403-8550-c993ca0f57d0",
    "name": "account_create/modify",
    "permission": [PERMISSION_SCOPE],
}

RESOURCE_RESPONSE = {
    "name": "modification",
    "owner": {
        "id": "55bed2c1-b35b-4704-aa6e-02afc4261eaa",
        "name": "organization-management",
    },
    "ownerManagedAccess": False,
    "_id": "5fa5e040-6eb3-44c2-a8ff-4f58729d10cc",
    "displayName": None,
    "attributes": {},
    "uris": [],
    "resource_scopes": [
        {
            "id": "eb966bbc-da86-45b8-a5c7-1ba7cf290e91",
            "name": "********-6118-4403-8550-c993ca0f57d0",
        }
    ],
    "scopes": [
        PERMISSION_BY_SCOPE_COMMON_RESPONSE,
    ],
}

SERVER_SCOPES = ["********-6118-4403-8550-c993ca0f57d0"]

MERGED_SCOPES = [
    "547642d4-0bac-4c14-94a0-a8e507c8e41c",
    "********-6118-4403-8550-c993ca0f57d0",
]

PAYLOAD_DATA = {
    "name": "modification",
    "resource_scopes": [
        "547642d4-0bac-4c14-94a0-a8e507c8e41c",
        "********-6118-4403-8550-c993ca0f57d0",
    ],
}

PERMISSION = {
    "id": "********-e432-4395-a9ad-1cadfd5b8a9f",
    "name": "account_create/modify",
    "type": "scope",
    "logic": "POSITIVE",
    "decisionStrategy": "UNANIMOUS",
    "config": {},
}

POLICY_DETAIL_RESPONSE = {
    "id": "e6865c9c-a267-44be-9187-40c12ca77141",
    "name": "ClientAdin",
    "description": "ClientAdmin",
    "type": "scope",
    "logic": "POSITIVE",
    "decisionStrategy": "AFFIRMATIVE",
    "config": {},
}

POLICY = {
    "name": "TestPolicy",
    "description": "TestPolicy",
    "type": "role",
    "logic": "POSITIVE",
    "decisionStrategy": "UNANIMOUS",
    "roles": [{"id": "3fa85f64-5717-4562-b3fc-2c963f66afa6", "required": False}],
}

POLICY_RESPONSE = {
    "id": "e6865c9c-a267-44be-9187-40c12ca77141",
    **POLICY,  # type: ignore
}


ROLE_WISE_USER_LIST = [
    {
        "id": "6c27fed2-2b12-42f7-b388-8f274fae28a2",
        "createdTimestamp": *************,
        "username": "service-account-organization-management",
        "enabled": True,
        "totp": False,
        "emailVerified": False,
        "disableableCredentialTypes": [],
        "requiredActions": [],
        "notBefore": 0,
    },
    {
        "id": "714d9410-6e91-4402-9665-77e1205842c2",
        "createdTimestamp": *************,
        "username": "<EMAIL>",
        "enabled": True,
        "totp": False,
        "emailVerified": True,
        "firstName": "Vedant",
        "lastName": "Mehta",
        "email": "<EMAIL>",
        "disableableCredentialTypes": [],
        "requiredActions": [],
        "notBefore": 0,
    },
    {
        "id": "b98565ba-1e50-4c12-8453-3439805971e6",
        "createdTimestamp": *************,
        "username": "admin",
        "enabled": True,
        "totp": False,
        "emailVerified": True,
        "firstName": "admin",
        "lastName": "admin",
        "email": "<EMAIL>",
        "attributes": {"asd": ["asd"]},
        "disableableCredentialTypes": [],
        "requiredActions": [],
        "notBefore": 0,
    },
]
