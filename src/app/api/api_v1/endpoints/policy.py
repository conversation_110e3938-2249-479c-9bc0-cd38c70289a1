import logging

from fastapi import APIRouter, Depends, HTTPException
from starlette import status

from app.adapters.iam import schemas
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import PolicyNotFound
from app.api import deps

logger = logging.getLogger(__name__)
router = APIRouter(tags=["admin"])


@router.get(
    "/policy", response_model=schemas.PolicyList, status_code=status.HTTP_200_OK
)
def get_policy(
    page: int | None = None,
    page_size: int | None = None,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> schemas.PolicyList:
    try:
        return iam.roles.get_policy_list(page=page, page_size=page_size)
    except PolicyNotFound as e:
        logger.error(f"Policy not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Policy not found.")
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )
