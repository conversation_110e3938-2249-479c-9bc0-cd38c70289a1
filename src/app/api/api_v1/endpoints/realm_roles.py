import logging

from fastapi import APIRouter, Depends, HTTPException
from keycloak_client import KeycloakConflict
from pydantic import UUID4
from starlette import status

from app.adapters.iam import schemas
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import (
    AlreadyExist,
    DefaultRoleError,
    MaxNumberInvalid,
    PermissionNotFound,
    PolicyAlreadyExist,
    PolicyNotFound,
    RoleAlreadyExist,
    RoleNotFound,
)
from app.api import deps
from app.api.decorators import measure_execution_time
from app.common import get_page_index
from app.schemas.auth import UserInfo

logger = logging.getLogger(__name__)
router = APIRouter(tags=["realm_role"])


@router.put("/role/permission", response_model=bool, status_code=status.HTTP_200_OK)
@measure_execution_time
def update_permissions_role(
    permission_role_data: schemas.UpdatePermissionRoleRequest,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> bool:
    try:
        scopes = iam.resources_admin.get_resource_scopes_and_permissions()
        user_role = actor.realm_access.roles
        if "DistributorAdmin" in user_role or "ClientAdmin" in user_role:
            permission_role_response = iam.roles.update_permissions_role(
                permission_role_data=permission_role_data, scopes=scopes
            )
        else:
            logger.error("Cannot create role.")
            raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized")
        logger.info(
            "**update_permissions_role Permission "
            f"Role Response:- {permission_role_response}"
        )
        logger.info("**update_permissions_role comming our of Endpoint layer")
        return permission_role_response
    except ValueError as e:
        logger.error(f"We Couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We Couldn't process your request."
        )
    except AlreadyExist as e:
        logger.error(f"Already exist error.: {str(e)}")
        raise HTTPException(
            status.HTTP_409_CONFLICT, detail="Role and Policy Already exist."
        )
    except PolicyAlreadyExist as e:
        logger.error(f" Role based Policy Already Exist error.: {str(e)}")
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            detail="Role based Policy Already Exist.",
        )
    except KeycloakConflict as e:
        logger.error(f"Create role confict occoured.: {str(e)}")
        raise HTTPException(
            status.HTTP_409_CONFLICT, detail="Create role confict occoured."
        )
    except PermissionNotFound as e:
        logger.error(f"Permission not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Permission not found.")
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.post(
    "/role",
    response_model=schemas.RolePolicyResponse,
    status_code=status.HTTP_201_CREATED,
)
@measure_execution_time
def create_role(
    role_data: schemas.RolePolicyRequest,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> schemas.RolePolicyResponse:
    try:
        """This function creates Role & Policy Both"""
        role_group = role_data.roleGroup
        MY_ORG = schemas.RoleGroup.MY_ORGANIZATION
        ACCOUNT = schemas.RoleGroup.ACCOUNT
        user_role = actor.realm_access.roles
        if "DistributorAdmin" in user_role or "ClientAdmin" in user_role:
            if role_group == MY_ORG or role_group == ACCOUNT:
                role_response = iam.roles.create_role_policy(role=role_data)
        else:
            logger.error("Cannot create role.")
            raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized")
        role_response.created_by = actor.email
        logger.info(f"**Role Response:- {role_response}")
        logger.info("**comming our of Endpoint layer")
        return role_response
    except ValueError as e:
        logger.error(f"We Couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We Couldn't process your request."
        )
    except AlreadyExist as e:
        logger.error(f"Already exist error.: {str(e)}")
        raise HTTPException(
            status.HTTP_409_CONFLICT, detail="Role and Policy Already exist."
        )
    except PolicyAlreadyExist as e:
        logger.error(f" Role based Policy Already Exist error.: {str(e)}")
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            detail="Role based Policy Already Exist.",
        )
    except RoleAlreadyExist as e:
        logger.error(f"Role already exist not found error.: {str(e)}")
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            detail=f"Role with name [{str(role_data.name)}] Already Exist.",
        )
    except KeycloakConflict as e:
        logger.error(f"Create role confict occoured.: {str(e)}")
        raise HTTPException(
            status.HTTP_409_CONFLICT, detail="Create role confict occoured."
        )
    except PermissionNotFound as e:
        logger.error(f"Permission not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Permission not found.")
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.put("/role/{role_id}", status_code=status.HTTP_201_CREATED)
def update_role(
    role_id: UUID4,
    role_data: schemas.RoleRequest,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> bool:
    try:
        scopes = iam.resources_admin.get_resource_scopes_and_permissions()
        return iam.roles.update_role(role_id=role_id, role=role_data, scopes=scopes)
    except ValueError as e:
        logger.error(f"We Couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We Couldn't process your request."
        )
    except PolicyNotFound as e:
        logger.error(str(e))
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Policy not found.")
    except PermissionNotFound as e:
        logger.error(f"Permission not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Permission not found.")
    except DefaultRoleError as e:
        logger.error(f"Default role update error.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            detail="Default role update operation not allowed.",
        )
    except RoleNotFound as e:
        logger.error(f"Roles not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Role not found.")
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get("/role", response_model=schemas.RolesList, status_code=status.HTTP_200_OK)
def get_realm_role(
    page: int | None = None,
    page_size: int | None = None,
    search: str | None = None,
    actor: deps.Actor = Depends(deps.get_actor),
    user_info: UserInfo = Depends(deps.get_user_info),
    iam: AbstractIAM = Depends(deps.get_iam),
) -> schemas.RolesList:
    try:
        user_role = actor.realm_access.roles
        page, page_size = get_page_index(page, page_size)
        if "ClientAdmin" in user_role:
            user_group = iam.users.get_user_assigned_groups(id=user_info.id)
            group_members = iam.groups.get_group_members(
                user_group[0].id, page, page_size
            )
            return iam.roles.get_group_roles(group_members, page, page_size, search)
        return iam.roles.get_roles_list(page, page_size, search)
    except MaxNumberInvalid as e:
        logger.error(f"Max Number should be multiple of 10 error.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="max Number should be a multiple of 10",
        )
    except RoleNotFound as e:
        logger.error(f"Roles not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Role not found.")
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get(
    "/role/{role_id}",
    response_model=schemas.RoleResponse,
    status_code=status.HTTP_200_OK,
)
def get_role_by_id(
    role_id: UUID4,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> schemas.RoleResponse:
    try:
        return iam.roles.get_role_by_id(role_id=role_id)
    except RoleNotFound as e:
        logger.error(f"Roles not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Role not found.")
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.delete(
    "/role/{role_id}",
    status_code=status.HTTP_204_NO_CONTENT,
)
def delete_role_by_id(
    role_id: UUID4,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
):
    try:
        return iam.roles.delete_role_by_id(role_id=role_id)
    except RoleNotFound as e:
        logger.error(f"Role not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Role not found.")
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except DefaultRoleError as e:
        logger.error(f"Default role deletion error.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            detail="Default role delete operation not allowed.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )
