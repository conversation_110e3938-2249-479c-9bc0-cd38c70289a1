import logging

from fastapi import APIRouter, Depends, HTTPException
from pydantic import UUID4
from starlette import status

from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import (
    GroupNotFound,
    MaxNumberInvalid,
    MemberNotFound,
    RoleNotFound,
    UnauthorizedUser,
)
from app.adapters.iam.schemas import (
    AssignGroupRoles,
    GroupMemberResponse,
    GroupsResponse,
    GroupSubgroup,
    RolesList,
)
from app.api import deps
from app.common import get_page_index

logger = logging.getLogger(__name__)
router = APIRouter(tags=["group"])


@router.get(
    "/group",
    response_model=GroupsResponse,
    status_code=status.HTTP_200_OK,
    description="Get group gives only groups and sub-groups",
)
def get_group_and_subgroup(
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> GroupsResponse:
    logger.info(f"actor{actor.realm_access.roles}")
    try:
        return iam.groups.groups_list()
    except GroupNotFound as e:
        logger.error(f"Group not found error.: {str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND, detail="Group details not found."
        )
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get(
    "/group/{group_id}/subgroups",
    response_model=list[GroupSubgroup],
    status_code=status.HTTP_200_OK,
    description="This endpoint gives only a group by a parent path not sub group.",
)
def subgroup_by_group(
    group_id: UUID4,
    page: int | None = None,
    page_size: int | None = None,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> list[GroupSubgroup]:
    match True:
        case _ if "DistributorAdmin" in actor.realm_access.roles:
            pass
        case _:
            logger.error("You are trying to access a restricted area.")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are trying to access a restricted area.",
            )
    try:
        return iam.groups.get_subgroup_by_group(group_id, page, page_size)
    except GroupNotFound as e:
        logger.error(f"Group details not found error.: {str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND, detail="Group details not found."
        )
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get(
    "/group/{group_id}/member",
    response_model=GroupMemberResponse,
    status_code=status.HTTP_200_OK,
)
def group_members(
    group_id: UUID4,
    page: int | None = None,
    page_size: int | None = None,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> GroupMemberResponse:
    if "DistributorAdmin" not in actor.realm_access.roles:
        logger.error("You are trying to access a restricted area.")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are trying to access a restricted area.",
        )

    try:
        page, page_size = get_page_index(page, page_size)
        group_members = iam.groups.get_group_members(group_id, page, page_size)
    except MaxNumberInvalid as e:
        logger.error(f"Max Number should be multiple of 10 error.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="max Number should be a multiple of 10",
        )
    except GroupNotFound as e:
        logger.error(f"Group not found error.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Group details not found."
        )
    except MemberNotFound as e:
        logger.error(f"Group members not found error.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Group members not found."
        )
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except UnauthorizedUser as e:
        logger.error(f"Unauthorized access.: {str(e)}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access.")
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )

    return group_members


@router.get(
    "/group/{group_id}/role",
    response_model=RolesList,
    status_code=status.HTTP_200_OK,
    deprecated=True,
)
def group_roles(
    group_id: UUID4,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> RolesList:
    try:
        group_roles = iam.groups.get_group_roles(group_id)
    except RoleNotFound as e:
        logger.error(f"Group roles not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Group roles not found.")
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )
    return group_roles


@router.post(
    "/group/{group_id}/roles-mapping",
    status_code=status.HTTP_201_CREATED,
    deprecated=True,
)
def assign_group_roles(
    group_id: UUID4,
    group_roles: AssignGroupRoles,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> RolesList:
    try:
        assign_group_roles = iam.groups.assign_group_roles(
            group_id=group_id, group_roles=group_roles
        )
    except RoleNotFound as e:
        logger.error(f"Roles assignment not found error.: {str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="Roles requested for assignment not found.",
        )
    except GroupNotFound as e:
        logger.error(f"Group not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Group not found")
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except UnauthorizedUser as e:
        logger.error(f"Unauthorized access.: {str(e)}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access.")
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )
    return assign_group_roles
