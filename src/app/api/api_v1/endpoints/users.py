import logging

from fastapi import APIRouter, Depends, HTTPException
from starlette import status

from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import (
    InvalidScopeException,
    MaxNumberInvalid,
    UnauthorizedUser,
    UsersNotFound,
)
from app.adapters.iam.schemas import (
    AuthorizedResponse,
    GroupMemberResponse,
    Scopes,
    UserScopeList,
)
from app.api import deps
from app.common import get_page_index
from app.schemas import TokenResponse, User, UserInfo
from app.services import mappers

logger = logging.getLogger(__name__)
router = APIRouter(tags=["user"])


@router.get("/user/evaluate", status_code=status.HTTP_200_OK)
def get_evaluated_user_scope(
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_token),
) -> TokenResponse:
    try:
        return iam.users.get_evaluated_user_scope(actor)
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except UnauthorizedUser as e:
        logger.error(f"Unauthorized access.: {str(e)}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access.")
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get("/user/me", response_model=User, status_code=status.HTTP_200_OK)
def read_authenticated_user(
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: UserInfo = Depends(deps.get_user_info),
) -> User:
    try:
        user_roles = iam.users.get_user_assigned_roles(id=user_info.id)
        user_groups = iam.users.get_user_assigned_groups(id=user_info.id)
        return mappers.user_info_to_user(user_info, user_roles, user_groups)
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get("/user/scope", response_model=UserScopeList, status_code=status.HTTP_200_OK)
def get_users_scopes(
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: UserInfo = Depends(deps.get_user_info),
) -> UserScopeList:
    """Get permissions associated with logged-in user only"""
    try:
        response = iam.users.get_users_scopes(id=user_info.id)
        return UserScopeList(result=response)
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get(
    "/users", response_model=GroupMemberResponse, status_code=status.HTTP_200_OK
)
def users(
    page: int | None = None,
    page_size: int | None = None,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_token),
) -> GroupMemberResponse:
    try:
        page, page_size = get_page_index(page, page_size)
        users = iam.users_admin.get_users(page=page, page_size=page_size)
    except MaxNumberInvalid as e:
        logger.error(f"Max Number should be multiple of 10 error.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="max Number should be a multiple of 10",
        )
    except UsersNotFound as e:
        logger.error(f"User not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="User not found.")
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )
    return users


@router.post(
    "/user/evaluate/scope",
    response_model=AuthorizedResponse,
    status_code=status.HTTP_200_OK,
)
def evaluate_authorized_scope(
    auth_scope: Scopes,
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: UserInfo = Depends(deps.get_user_info),
):
    "Need to pass minimum and maximum 1 Scope into the list"
    try:
        return iam.users.get_authorized_scope(auth_scope, user_info.id)
    except InvalidScopeException as e:
        logger.error(f"Invalid scope {auth_scope.scope}. Error: {str(e)}")
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=str(e))
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )
