import logging

from fastapi import APIRouter, Depends, HTTPException
from starlette import status

from app.adapters.iam import schemas
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import RoleNotFound
from app.api import deps

logger = logging.getLogger(__name__)
router = APIRouter(tags=["admin"])


@router.get("/client/role", status_code=status.HTTP_200_OK)
def get_client_role(
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> schemas.ClientRoleList:
    match True:
        case _ if "DistributorAdmin" in actor.realm_access.roles:
            pass
        case _:
            logger.error("You are trying to access a restricted area.")
            raise HTTPException(
                status.HTTP_403_FORBIDDEN,
                detail="You are trying to access a restricted area.",
            )
    try:
        role_data = iam.clients.get_client_roles()
        return schemas.ClientRoleList(result=role_data)
    except RoleNotFound as e:
        logger.error(f"Role not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Roles not found.")
    except ValueError as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )
