import logging

from fastapi import APIRouter, HTTPException, Path
from starlette import status

from app.adapters.external_api.worldov import worldov_adapter
from app.schemas.sim import SimDataSessionResponse
from app.services.sim_service import SimService

logger = logging.getLogger(__name__)
router = APIRouter(tags=["sim"])

# Initialize the SIM service with the WorldOV adapter
sim_service = SimService(worldov_adapter)


@router.get(
    "/sim/data-sessions/{iccid}/latest",
    response_model=SimDataSessionResponse,
    status_code=status.HTTP_200_OK,
    summary="Get latest data session for SIM",
    description="Retrieve the latest data session information for a SIM card by ICCID"
)
async def get_latest_data_session(
    iccid: str = Path(..., description="The ICCID of the SIM card", min_length=15, max_length=22)
) -> SimDataSessionResponse:
    """
    Get the latest data session for a SIM card by ICCID.
    
    This endpoint acts as a proxy to the WorldOV API and retrieves the most recent
    data session information for the specified SIM card.
    
    Args:
        iccid: The ICCID (Integrated Circuit Card Identifier) of the SIM card
        
    Returns:
        SimDataSessionResponse: Contains the data session information including:
        - Session ID and partner information
        - Network details (IMSI, APN, IP address)
        - Data usage statistics
        - Location information (MCC, MNC, LAC, Cell ID)
        - Session timestamps
        
    Raises:
        HTTPException: 
            - 400: Invalid ICCID format
            - 404: Data session not found for the given ICCID
            - 401: Unauthorized access to external API
            - 500: Internal server error or external API error
    """
    try:
        logger.info(f"Received request for latest data session, ICCID: {iccid}")
        
        # Call the service layer
        response = await sim_service.get_latest_data_session(iccid)
        
        # Handle different response scenarios
        if not response.success:
            if "Invalid ICCID" in response.message:
                logger.error(f"Invalid ICCID provided: {iccid}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=response.message
                )
            elif "not found" in response.message.lower():
                logger.error(f"Data session not found for ICCID: {iccid}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=response.message
                )
            elif "Unauthorized" in response.message:
                logger.error("Unauthorized access to external API")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Unauthorized access to external service"
                )
            else:
                logger.error(f"Service error for ICCID {iccid}: {response.message}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to retrieve data session"
                )
        
        logger.info(f"Successfully processed request for ICCID: {iccid}")
        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing request for ICCID {iccid}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )