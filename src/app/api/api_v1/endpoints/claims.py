import logging

from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException
from keycloak_client import KeycloakConflict
from pydantic import UUID4
from starlette import status

from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import (
    PermissionNotFound,
    PolicyNotFound,
    ResourceNotFound,
    ScopeNotFound,
    UnauthorizedUser,
)
from app.adapters.iam.schemas import (
    ClaimPermission,
    ClaimPermissionResponse,
    PermissionDetails,
)
from app.api import deps

logger = logging.getLogger(__name__)
router = APIRouter(tags=["resource/scope/permission"])


@router.post(
    "/resource/scope/permission",
    status_code=status.HTTP_201_CREATED,
    response_model=ClaimPermissionResponse,
)
def create_permission(
    claim_permission_data: ClaimPermission,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> ClaimPermissionResponse:
    match True:
        case _ if "DistributorAdmin" in actor.realm_access.roles:
            pass
        case _:
            logger.error("You are trying to access a restricted area.")
            raise HTTPException(
                status.HTTP_403_FORBIDDEN,
                detail="You are trying to access a restricted area.",
            )
    try:
        claim_permission_data_response = iam.claim_permission_admin.create_permission(
            claim_permission_admin=claim_permission_data
        )
    except KeycloakConflict as e:
        logging.error(
            f"{str(e)} Error: Permission with name '{claim_permission_data.name}'"
            "requested to create already exist."
        )
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            detail=f"Permission with name '{claim_permission_data.name}' requested"
            " to create already exist.",
        )
    except ValueError as e:
        logger.error(f"We Couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We Couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )
    return claim_permission_data_response


@router.get("/resource/scope/{scope_id}/permission", status_code=status.HTTP_200_OK)
def get_permission_by_scope(
    scope_id: UUID4,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> PermissionDetails:
    match True:
        case _ if "DistributorAdmin" in actor.realm_access.roles:
            pass
        case _:
            logger.error("You are trying to access a restricted area.")
            raise HTTPException(
                status.HTTP_403_FORBIDDEN,
                detail="You are trying to access a restricted area.",
            )
    try:
        return iam.claim_permission_admin.get_permission(scope_id)
    except ScopeNotFound as e:
        logger.error(f"Scope not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Scope not found.")
    except PermissionNotFound as e:
        logger.error(f"Permission not found error.: {str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND, detail="Permission data not found."
        )
    except ValueError as e:
        logger.error(f"We Couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We Couldn't process your request."
        )
    except ResourceNotFound as e:
        logger.error(f"Resource not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Resource not found.")
    except UnauthorizedUser as e:
        logger.error(f"Unauthorized access.: {str(e)}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access.")
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )


@router.get("/policy/{policy_id}/permission", status_code=status.HTTP_200_OK)
def get_permission_by_policy(
    policy_id: UUID4,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> PermissionDetails:
    try:
        return iam.claim_permission_admin.get_permission_by_policy(policy_id)
    except PolicyNotFound as e:
        logger.error(f"Policy not found.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Policy not found.")
    except ValueError as e:
        logger.error(f"We Couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We Couldn't process your request."
        )
    except UnauthorizedUser as e:
        logger.error(f"Unauthorized access.: {str(e)}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access.")
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )
