import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from keycloak_client import KeycloakConflict
from pydantic import UUID4
from starlette import status

from app.adapters.iam import schemas
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import (
    PermissionNotFound,
    PolicyNotFound,
    ResourceAlreadyExist,
    ResourceNotFound,
    ResourceScopePermissionNotFound,
    ScopeAlreadyExist,
    ScopeNotFound,
    UnauthorizedUser,
)
from app.api import deps

logger = logging.getLogger(__name__)
router = APIRouter(tags=["resource/scope/permission"])


@router.post(
    "/resource",
    response_model=schemas.ResourceResponse,
    status_code=status.HTTP_201_CREATED,
)
def create_resource(
    resouce_data: schemas.Resource,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> schemas.ResourceResponse:
    match True:
        case _ if "DistributorAdmin" in actor.realm_access.roles:
            pass
        case _:
            logger.error("You are trying to access a restricted area.")
            raise HTTPException(
                status.HTTP_403_FORBIDDEN,
                detail="You are trying to access a restricted area.",
            )
    try:
        return iam.resource.create_resources(resource=resouce_data)
    except ValueError as e:
        logger.error(f"We couldn't process the request. {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process the request."
        )
    except KeycloakConflict as e:
        logger.error(f"Resource is already created.: {str(e)}")
        raise HTTPException(
            status.HTTP_409_CONFLICT, detail="Resource is already created."
        )
    except ScopeAlreadyExist as e:
        logger.error(f"Scope already exist.: {str(e)}")
        raise HTTPException(status.HTTP_409_CONFLICT, detail="Scope already exist.")
    except ResourceAlreadyExist as e:
        logger.error(f"Resource already exist.: {str(e)}")
        raise HTTPException(status.HTTP_409_CONFLICT, detail="Resource already exist.")
    except Exception as e:
        logger.error(f"Couldn't process the request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.put("/resource/{resource_id}", status_code=status.HTTP_204_NO_CONTENT)
def update_resource(
    resource_id: UUID4,
    resource_scopes: schemas.ScopeList,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
):
    match True:
        case _ if "DistributorAdmin" in actor.realm_access.roles:
            pass
        case _:
            logger.error("You are trying to access a restricted area.")
            raise HTTPException(
                status.HTTP_403_FORBIDDEN,
                detail="You are trying to access a restricted area.",
            )
    try:
        return iam.resource.update_resource(resource_id, resource_scopes)
    except ValueError as e:
        logger.error(f"We couldn't process the request. {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process the request."
        )
    except ResourceNotFound as e:
        logger.error(f"Resource not found while updating resource.: {str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="Resource not found while updating resource.",
        )
    except ScopeAlreadyExist as e:
        logger.error(f"Scope already exist.: {str(e)}")
        raise HTTPException(status.HTTP_409_CONFLICT, detail="Scope already exist.")
    except UnauthorizedUser as e:
        logger.error(f"Unauthorized access {str(e)}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access.")
    except Exception as e:
        logger.error(f"Couldn't process the request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )


@router.get("/resource", status_code=status.HTTP_200_OK)
def get_resource(
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> schemas.ResourceList:
    match True:
        case _ if "DistributorAdmin" in actor.realm_access.roles:
            pass
        case _:
            logger.error("You are trying to access a restricted area.")
            raise HTTPException(
                status.HTTP_403_FORBIDDEN,
                detail="You are trying to access a restricted area.",
            )
    try:
        result = iam.resources_admin.get_resource()
        return schemas.ResourceList(result=result)
    except ValueError as e:
        logger.error(f"We couldn't process the request. {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process the request."
        )
    except ResourceNotFound as e:
        logger.error(f"Resource not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Resource not found.")
    except UnauthorizedUser as e:
        logger.error(f"Unauthorized access {str(e)}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access.")
    except Exception as e:
        logger.error(f"Couldn't process the request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )


@router.get("/resource/{resource_id}", status_code=status.HTTP_200_OK)
def get_resource_by_id(
    resource_id: UUID4,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> schemas.ResourceResponse:
    match True:
        case _ if "DistributorAdmin" in actor.realm_access.roles:
            pass
        case _:
            logger.error("You are trying to access a restricted area.")
            raise HTTPException(
                status.HTTP_403_FORBIDDEN,
                detail="You are trying to access a restricted area.",
            )
    try:
        return iam.resources_admin.get_resource_by_id(resource_id)
    except ValueError as e:
        logger.error(f"We Couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We Couldn't process your request."
        )
    except ResourceNotFound as e:
        logger.error(f"Resource not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Resource not found.")
    except UnauthorizedUser as e:
        logger.error(f"Unauthorized access.: {str(e)}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access.")
    except Exception as e:
        logger.error(f"Couldn't process the request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )


@router.get("/resource/scope/permission", status_code=status.HTTP_200_OK)
def get_resource_scope_permission(
    iam: AbstractIAM = Depends(deps.get_iam),
) -> schemas.ResourceList:
    try:
        result_data = iam.resources_admin.get_resource_scopes_and_permissions()
        return schemas.ResourceList(result=result_data)
    except ValueError as e:
        logger.error(f"We Couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We Couldn't process your request."
        )
    except ResourceNotFound as e:
        logger.error(f"Resource not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Resource not found.")
    except ResourceScopePermissionNotFound as e:
        logger.error(f"Resource scope permission not found error.: {str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND, detail="Resource scope permission not found."
        )
    except UnauthorizedUser as e:
        logger.error(f"Unauthorized access.: {str(e)}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access.")
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )


@router.get(
    "/resource/policy/permission/{permission_id}", status_code=status.HTTP_200_OK
)
def get_policy_by_permission(
    permission_id: UUID4,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
) -> schemas.PolicyList:
    if "DistributorAdmin" not in actor.realm_access.roles:
        logger.error("You are trying to access a restricted area.")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are trying to access a restricted area.",
        )
    try:
        result_data = iam.resources_admin.get_policies_by_permission(permission_id)
        return schemas.PolicyList(result=result_data)
    except PermissionNotFound as e:
        logger.error(f"Permission not found error.: {str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND, detail="Permission data not found."
        )
    except PolicyNotFound as e:
        logger.error(f"Policy not found error by permission id.:{str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="No policies found for requested permission.",
        )
    except ValueError as e:
        logger.error(f"We Couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We Couldn't process your request."
        )
    except UnauthorizedUser as e:
        logger.error(f"Unauthorized access.: {str(e)}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access.")
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )


@router.delete("/resource/{resource_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_resource_by_id(
    resource_id: UUID4,
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
):
    match True:
        case _ if "DistributorAdmin" in actor.realm_access.roles:
            pass
        case _:
            logger.error("You are trying to access a restricted area.")
            raise HTTPException(
                status.HTTP_403_FORBIDDEN,
                detail="You are trying to access a restricted area.",
            )
    try:
        return iam.resources_admin.delete_resource_by_id(resource_id=resource_id)
    except ResourceNotFound as e:
        logger.error(str(e))
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Resource not found.")
    except ScopeNotFound as e:
        logger.error(f"Scope not found error.: {str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="Scope not found while deleting the resource.",
        )
    except UnauthorizedUser as e:
        logger.error(f"Unauthorized access.: {str(e)}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access.")
    except ValueError as e:
        logger.error(f"We Couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )
