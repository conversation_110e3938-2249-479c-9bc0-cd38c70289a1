import logging
from collections.abc import Generator
from typing import Any, TypeVar

import authn
import httpx
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2AuthorizationCodeBearer
from keycloak_client import KeycloakAdmin
from pydantic import BaseModel, ValidationError

from app import schemas
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.keycloak import KeycloakIAM
from app.core.config import settings

if not settings.OIDC_TOKEN_URL:
    raise ValueError("OIDC_TOKEN_URL is not set")
if not settings.OIDC_AUTHORIZATION_URL:
    raise ValueError("OIDC_AUTHORIZATION_URL is not set")
logger = logging.getLogger(__name__)

print(f"OIDC_TOKEN_URL: {settings.OIDC_TOKEN_URL}")
print(f"OIDC_AUTHORIZATION_URL:{settings.OIDC_AUTHORIZATION_URL}")
oauth2_scheme = OAuth2AuthorizationCodeBearer(
    tokenUrl=settings.OIDC_TOKEN_URL,
    authorizationUrl=settings.OIDC_AUTHORIZATION_URL,
)


def get_http_client() -> Generator[httpx.Client, None, None]:
    with httpx.Client() as client:
        yield client


def get_keycloak_admin(
    http_client: httpx.Client = Depends(get_http_client),
) -> KeycloakAdmin:
    return KeycloakAdmin(
        http_client,
        keycloak_url=settings.KEYCLOAK_URL,
        realm=settings.KEYCLOAK_REALM,
        client_id=settings.OIDC_CLIENT_ID,
        client_secret=settings.OIDC_CLIENT_SECRET,
    )


def get_iam(ka: KeycloakAdmin = Depends(get_keycloak_admin)) -> AbstractIAM:
    logger.info(f"ka info{ka}")
    return KeycloakIAM(ka)


def get_token(token: str = Depends(oauth2_scheme)) -> str:
    return token


def introspect_token(token: str = Depends(oauth2_scheme)) -> dict[str, Any]:
    try:
        payload = authn.introspect_token(
            token=token,
            url=str(settings.OIDC_TOKEN_INTROSPECTION_URL),
            client_id=settings.OIDC_CLIENT_ID,
            client_secret=settings.OIDC_CLIENT_SECRET,
        )
        if not payload.get("active", False):
            raise authn.AuthNError("Invalid token")

        expected_type = "Bearer"
        token_type = payload.get("typ")
        if not token_type:
            raise authn.AuthNError('Invalid token: missing "typ"')
        elif token_type != expected_type:
            raise authn.AuthNError(
                f'Invalid token type: "{token_type}".' f" Expected: {expected_type}"
            )
    except authn.AuthNError as e:
        logging.warning(f"Bad token: {e}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
    else:
        return payload


Actor = schemas.UserInfo


def get_actor(
    payload: dict[str, Any] = Depends(introspect_token),
) -> Actor:
    logger.info(f"payload{payload}")
    try:
        if "clientId" in payload:
            logger.info("service info")
            return get_service_info(payload)
        logger.info("User info")
        return get_user_info(payload)
    except TypeError as e:
        logging.warning(f"Bad token payload: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We Couldn't process your request.",
        )


def get_user_info(
    payload: dict[str, Any] = Depends(introspect_token),
) -> schemas.UserInfo:
    try:
        return _parse_token_payload(payload, schemas.UserInfo)
    except TypeError as e:
        logging.warning(f"Bad token payload: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We Couldn't process your request.",
        )


def get_service_info(
    payload: dict[str, Any] = Depends(introspect_token),
) -> schemas.UserInfo:
    return _parse_token_payload(payload, schemas.UserInfo)


ActorInfoT = TypeVar("ActorInfoT", bound=BaseModel)


def _parse_token_payload(
    payload: dict[str, Any],
    schema: type[ActorInfoT],
) -> ActorInfoT:
    if not isinstance(payload, dict):
        raise TypeError(
            f"Expected payload to be a dict, but got {type(payload).__name__}"
        )
    try:
        obj = schema.parse_obj(payload)
    except ValidationError as e:
        logging.warning(f"Bad token payload for {schema}:{e}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED)
    else:
        return obj
