import logging
import runpy
import sys


def run_scope_permission_script():
    try:
        sys.argv = [
            "scope_permission_creation_dynamic_script",
            "--roles",
            "ClientAdmin",
            "Client_ReadOnly",
            "DistributorAdmin",
            "Distributor_ReadOnly",
            "--grouped_resources",
            "SIM Details",
            "GET/v1/glass/sim/connection/history",
            "View Connection History API",
            "--grouped_resources",
            "SIM Details",
            "GET/v1/glass/sim/voice/connection/history",
            "View Voice Connection History API",
            "--grouped_resources",
            "SIM Details",
            "GET/v1/glass/sim/sms/connection/history",
            "View SMS Connection History API",
        ]

        runpy.run_module(
            "script.scope_permission_creation_dynamic_script", run_name="__main__"
        )
    except Exception as e:
        logging.error(f"Error: {e}")


if __name__ == "__main__":
    run_scope_permission_script()


"""
cd src/
run this command:- python -m script.21_05_2025_make_connection_history_api_permissions

"""
