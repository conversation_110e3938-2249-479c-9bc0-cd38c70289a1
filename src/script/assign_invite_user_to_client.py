import logging

import requests

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class MigrationError(BaseException):
    ...


# This migration script will add permissions to ClientAdmin.
# It is intended to be run after the migration script has been run.
class MigrationService:
    def __init__(
        self,
    ):
        ...

    def create_migration(self) -> bool:
        try:
            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
            CLIENT_ID = settings.KEYCLOAK_CLIENT_ID

            policies_to_search = [
                "ClientAdmin",
            ]

            permission_to_assign = [
                "Invite User",
            ]

            headers = {"Authorization": f"Bearer {access_token}"}

            # Get Policy id by policy name
            policy_id = None
            for policy_name in policies_to_search:
                url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                    f"resource-server/policy?name={policy_name}"
                )

                policy_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                policy_response = policy_response.json()
                if len(policy_response) == 0:
                    raise MigrationError(f"Policy not found with name: {policy_name}")
                policy_id = policy_response[0]["id"]

            for permission_name in permission_to_assign:
                url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                    f"resource-server/permission?name={permission_name}"
                )
                permission_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )

                if permission_response.status_code == 200:
                    permission_data = permission_response.json()[0]
                    permission_id = permission_data["id"]

                    # Getting Resource related to permission_id
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"
                        f"/policy/{permission_id}/resources"
                    )
                    resources = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )  # This will return list of resource for the given permission id
                    if resources.status_code == 200:
                        resource_list = resources.json()
                        resource_ids = []
                        for resource in resource_list:
                            resource_ids.append(resource["_id"])

                    # Getting scopes related to permission_id
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"
                        f"/policy/{permission_id}/scopes"
                    )
                    scopes = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )  # This will return list of scopes for the given permission id
                    if scopes.status_code == 200:
                        scope_list = scopes.json()
                        scope_ids = []
                        for scope in scope_list:
                            scope_ids.append(scope["id"])

                    # Getting Policies related to permission_id
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"
                        f"/policy/{permission_id}/associatedPolicies"
                    )
                    policies = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )  # This will return list of policy for the given permission id
                    if policies.status_code == 200:
                        policy_list = policies.json()
                        permission_policy_ids = []
                        for policy in policy_list:
                            permission_policy_ids.append(policy["id"])
                        if policy_id not in permission_policy_ids:
                            permission_policy_ids.append(policy_id)

                            # Adding policy to the permission
                            url = (
                                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                                f"resource-server/permission/scope/{permission_id}"
                            )
                            data = {
                                "id": permission_id,
                                "name": permission_data["name"],
                                "description": permission_data["description"],
                                "type": "scope",
                                "logic": "POSITIVE",
                                "decisionStrategy": "AFFIRMATIVE",
                                "resources": resource_ids,
                                "scopes": scope_ids,
                                "policies": permission_policy_ids,
                            }
                            response = requests.put(
                                url,
                                headers=headers,
                                json=data,
                                timeout=settings.TIMEOUT,
                            )
                            if response.status_code != 201:
                                raise MigrationError()
                            logging.info(f"{permission_name} added to ClientAdmin")
            return True
        except Exception as e:
            raise MigrationError(str(e))


if __name__ == "__main__":
    migration = MigrationService()
    migration.create_migration()
    logging.info("Successfully Added permissions to ClientAdmin.")

# To run this script you need to run this command
# python -m script.assign_account_names_to_client
