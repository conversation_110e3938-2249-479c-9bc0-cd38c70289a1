# Created on 2024-04-03 16:47:00 IST

import logging

import requests

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class MigrationError(BaseException):
    ...


class MigrationService:
    def __init__(
        self,
    ):
        ...

    def create_migration(self) -> bool:
        try:

            BASE_URL = f"{settings.BASE_URL}{settings.KEYCLOAK_REALM}"
            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
            CLIENT_ID = settings.KEYCLOAK_CLIENT_ID

            roles_to_search = ["DistributorAdmin", "Distributor_ReadOnly"]

            # Step 1: Resource and it's scope creation.
            resources_to_search = {
                "SIM Details": [
                    "GET/v1/glass/accounts/sim/cards/info/{search}",
                ],
            }
            proper_names = {
                "GET/v1/glass/accounts/sim/cards/info/{search}": "View SIM Details",
            }
            for resource, scopes in resources_to_search.items():
                # Step 1.1: Scope creation.
                for scope in scopes:
                    proper_name = proper_names.get(f"{scope}")
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                        f"resource-server/scope"
                    )
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }
                    data = {
                        "name": f"{scope}",
                        "displayName": f"{proper_name}",
                    }
                    response = requests.post(
                        url, headers=headers, json=data, timeout=settings.TIMEOUT
                    )
                    if response.status_code == 201:
                        logging.info(f"Scope {scope} created.")
                    if response.status_code != 201 and response.status_code != 409:
                        raise MigrationError()

                # Step 1.2: Resource and associated Scope Updation.
                resource_response_data = []
                url = (
                    f"{BASE_ADMIN_URL}/clients/"
                    f"{CLIENT_ID}/authz/resource-server/resource?"
                    f"deep=false&first=0&max=20&name={resource}"
                )
                headers = {"Authorization": f"Bearer {access_token}"}

                resource_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                resource_response_data = resource_response.json()
                resource_id = resource_response_data[0]["_id"]

                # Step 1.3: Get Existing Scopes
                resource_scope_url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/"
                    f"authz/resource-server/resource/{resource_id}"
                )
                get_headers = {"Authorization": f"Bearer {access_token}"}
                resource_data = requests.get(
                    resource_scope_url, headers=get_headers, timeout=settings.TIMEOUT
                ).json()
                existing_scopes = [scope["name"] for scope in resource_data["scopes"]]

                update_url = f"{BASE_URL}/authz/protection/resource_set/{resource_id}"
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}",
                }
                existing_scopes.append(scopes[0])
                data = {
                    "_id": f"{resource_id}",
                    "name": f"{resource}",
                    "resource_scopes": existing_scopes,
                }
                response = requests.put(
                    update_url, headers=headers, json=data, timeout=settings.TIMEOUT
                )
                if response.status_code == 204:
                    logging.info(f"Resource {resource} created.")
                if response.status_code != 204 and response.status_code != 409:
                    raise MigrationError()

            # Step 2: Permission creation.
            role_policy_list = []
            for role_name in roles_to_search:
                policy_response_data = []
                url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}"
                    f"/authz/resource-server/policy?"
                    f"first=0&max=10&name={role_name}&permission=false"
                )
                headers = {"Authorization": f"Bearer {access_token}"}

                policy_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                policy_response_data = policy_response.json()

                if len(policy_response_data) != 0 and "id" in policy_response_data[0]:
                    policy_ids = [policy["id"] for policy in policy_response_data]
                    role_policy_list.extend(policy_ids)

            for resource, scopes in resources_to_search.items():
                for scope in scopes:
                    scope_response_data = []
                    url = (
                        f"{BASE_ADMIN_URL}/clients/"
                        f"{CLIENT_ID}/authz/resource-server/scope?"
                        f"deep=false&first=0&max=20&name={scope}"
                    )
                    headers = {"Authorization": f"Bearer {access_token}"}

                    scope_response = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )
                    scope_response_data = scope_response.json()
                    scope_id = scope_response_data[0]["id"]

                    proper_name = proper_names.get(f"{scope}")

                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}"
                        f"/authz/resource-server/permission/scope"
                    )
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }
                    data = {
                        "type": "scope",
                        "logic": "POSITIVE",
                        "decisionStrategy": "AFFIRMATIVE",
                        "name": f"{proper_name}",
                        "description": f"{proper_name}",
                        "resources": [f"{resource_id}"],
                        "scopes": [f"{scope_id}"],
                        "policies": role_policy_list,
                    }
                    response = requests.post(
                        url, headers=headers, json=data, timeout=settings.TIMEOUT
                    )
                    if response.status_code != 201 and response.status_code != 409:
                        raise MigrationError()
                    logging.info(f"Permission {proper_name} created inside {resource}.")

            return True
        except Exception as e:
            raise MigrationError(str(e))


if __name__ == "__main__":
    migration = MigrationService()
    migration.create_migration()


# Run the script
# python -m script.add_sim_info_permission
