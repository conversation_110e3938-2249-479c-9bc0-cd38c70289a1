import logging

import requests

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class MigrationError(BaseException):
    ...


# This migration script will add description in Roles.
class MigrationService:
    def __init__(
        self,
    ):
        ...

    def create_migration(self) -> bool:
        try:
            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"

            roles_to_search = {
                "Client_ReadOnly": "Read-only permissions to the portal",
                "Distributor_ReadOnly": "Read-only permissions to the portal",
                "ClientAdmin": "Full access to the portal",
                "DistributorAdmin": "Full access to the portal",
            }

            headers = {"Authorization": f"Bearer {access_token}"}

            for (role_name, role_description) in roles_to_search.items():
                url = f"{BASE_ADMIN_URL}/roles?search={role_name}"

                role_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                role_response = role_response.json()
                if len(role_response) == 0:
                    raise MigrationError(f"Role not found with name: {role_name}")
                role_id = role_response[0]["id"]

                # Update role description
                url = f"{BASE_ADMIN_URL}/roles-by-id/{role_id}"
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}",
                }
                data = {
                    "id": role_id,
                    "name": role_name,
                    "description": role_description,
                    "composite": False,
                    "clientRole": False,
                    "containerId": "master",
                    "attributes": {},
                }
                response = requests.put(
                    url, headers=headers, json=data, timeout=settings.TIMEOUT
                )
                if response.status_code != 204:
                    raise MigrationError(
                        f"Role {role_name} not updated with description {role_description}"  # noqa
                    )
                logging.info(
                    f"Role {role_name} updated with description {role_description}"
                )

            return True
        except Exception as e:
            raise MigrationError(str(e))


if __name__ == "__main__":
    migration = MigrationService()
    migration.create_migration()
    logging.info("Successfully updated description.")

# To run this script you need to run this command
# python -m script.add_description_in_roles
