import logging

import requests

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class MigrationError(BaseException):
    ...


class MigrationService:
    def __init__(
        self,
    ):
        ...

    def create_migration(self) -> bool:
        try:

            BASE_URL = f"{settings.BASE_URL}{settings.KE<PERSON>CLOAK_REALM}"
            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
            CLIENT_ID = settings.KEYCLOAK_CLIENT_ID

            # Step 2: Based on default role policy creation.

            roles_to_search = [
                "DistributorAdmin",
                "ClientAdmin",
            ]

            policies_to_search = [
                "DistributorAdmin",
                "DistributorUser",
                "ClientAdmin",
                "ClientUser",
            ]

            for role_name in policies_to_search:
                role_response_data = []
                url = f"{BASE_ADMIN_URL}/roles?search={role_name}"
                headers = {"Authorization": f"Bearer {access_token}"}

                role_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                role_response_data = role_response.json()

                if len(role_response_data) != 0:
                    role_id = role_response_data[0]["id"]

                    # Step 3: Policy create.
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                        f"resource-server/policy/role"
                    )
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }

                    data = {
                        "name": f"{role_name}",
                        "description": f"{role_name}",
                        "type": "role",
                        "logic": "POSITIVE",
                        "decisionStrategy": "UNANIMOUS",
                        "roles": [{"id": f"{role_id}"}],
                    }
                    response = requests.post(
                        url, headers=headers, json=data, timeout=settings.TIMEOUT
                    )
                    if response.status_code != 201 and response.status_code != 409:
                        raise MigrationError()

            # Step 4: Resource and it's scope creation.

            resources_to_search = {
                "Accounts": [
                    "GET/v1/glass/accounts",
                    "GET/v1/glass/accounts/{account_id}",
                    "POST/v1/glass/accounts",
                    "PUT/v1/glass/accounts/{account_id}",
                    "DELETE/v1/glass/accounts/{account_id}",
                ],
                "SIM Details": [
                    "GET/v1/glass/sim/cards/usage",
                    "GET/v1/glass/account-names",
                    "POST/v1/glass/sim/cards/activate",
                    "POST/v1/glass/sim/cards/deactivate",
                    "GET/v1/glass/sim/cards/status/{imsi}",
                    "GET/v1/glass/sim/cards/remains",
                    "GET/v1/glass/sim/cards/summary/{imsi}",
                    "GET/v1/glass/sim/connection/history",
                    "GET/v1/glass/sim/sms/connection/history",
                    "GET/v1/glass/sim/voice/connection/history",
                ],
                "Users": [
                    "GET/v1/glass/authorization/role",
                    "POST/v1/glass/authorization/role",
                    "DELETE/v1/glass/authorization/role",
                    "GET/v1/glass/authorization/{role_id}/permission",
                    "POST/v1/glass/accounts/{account_id}/invites",
                ],
                "Rate Plans": [
                    "POST/v1/glass/rate-plans",
                    "GET/v1/glass/rate-plans/{id}",
                    "PUT/v1/glass/rate-plans/{id}",
                    "DELETE/v1/glass/rate-plans/{id}",
                    "GET/v1/glass/rate-plans/by-accounts/{account_id}",
                    "GET/v1/glass/rate-plans/by-accounts",
                    "GET/v1/glass/rate-plans",
                ],
                "Billing": [
                    "GET/v1/glass/billing/invoices",
                    "POST/v1/glass/billing/cycles/{month}",
                    "PUT/v1/glass/billing/invoices/{id}/published",
                    "GET/v1/glass/billing/invoices/{id}",
                    "GET/v1/glass/billing/invoices/{id}/usage",
                    "POST/v1/glass/billing/invoices/{id}/adjustments",
                    "PUT/v1/glass/billing/invoices/{invoice_id}/adjustments/{id}",
                    "DELETE/v1/glass/billing/invoices/{invoice_id}/adjustments/{id}",
                ],
                "SIM Orders": [
                    "POST/v1/glass/sim/allocations/{allocation_reference}/account/{account_id}/rateplan/{rateplan_id}/custom",  # noqa
                    "GET/v1/glass/sim/allocations",
                    "POST/v1/glass/sim/documents",
                    "GET/v1/glass/sim/ranges",
                ],
                "Reporting": [
                    "GET/v1/glass/sim/cards/usage/export",
                    "GET/v1/glass/sim/connection/history/export",
                    "GET/v1/glass/sim/sms/connection/history/export",
                    "GET/v1/glass/sim/voice/connection/history/export",
                    "GET/v1/glass/billing/invoices/{id}/usage/export",
                    "GET/v1/glass/market/accounts",
                    "GET/v1/glass/market/accounts/imsi",
                    "GET/v1/glass/market/account/{account_id}",
                ],
                "Audit": [
                    "GET/v1/glass/audit/{account_id}",
                    "GET/v1/glass/sim/cards/audit",
                ],
            }
            proper_names = {
                "GET/v1/glass/accounts": "View Accounts",
                "GET/v1/glass/accounts/{account_id}": "View Account Details",
                "POST/v1/glass/accounts": "Create Account",
                "PUT/v1/glass/accounts/{account_id}": "Modify Account",
                "DELETE/v1/glass/accounts/{account_id}": "Delete Account",
                "GET/v1/glass/sim/cards/usage": "View SIM Information",
                "GET/v1/glass/account-names": "View Account Names",
                "POST/v1/glass/sim/cards/activate": "SIM Activate",
                "POST/v1/glass/sim/cards/deactivate": "SIM Deactivate",
                "GET/v1/glass/sim/cards/status/{imsi}": "View SIM Status",
                "GET/v1/glass/sim/cards/remains": "View SIM Remains",
                "GET/v1/glass/sim/cards/summary/{imsi}": "View SIM Connection Summary",
                "GET/v1/glass/sim/connection/history": "View Connection History",
                "GET/v1/glass/sim/sms/connection/history": "View SMS Connection History",  # noqa
                "GET/v1/glass/sim/voice/connection/history": "View Voice Connection History",  # noqa
                "GET/v1/glass/authorization/role": "View Roles",
                "POST/v1/glass/authorization/role": "Create Role",
                "DELETE/v1/glass/authorization/role": "Delete Role",
                "GET/v1/glass/authorization/{role_id}/permission": "View Permission By Role",  # noqa
                "POST/v1/glass/accounts/{account_id}/invites": "Invite User",
                "POST/v1/glass/rate-plans": "Create Rate Plan",
                "GET/v1/glass/rate-plans/{id}": "View Rate Plan",
                "PUT/v1/glass/rate-plans/{id}": "Modify Rate Plan",
                "DELETE/v1/glass/rate-plans/{id}": "Delete Rate Plan",
                "GET/v1/glass/rate-plans/by-accounts/{account_id}": "Account Rate Plans",  # noqa
                "GET/v1/glass/rate-plans/by-accounts": "View Rate Plans",
                "GET/v1/glass/rate-plans": "View Client Rate Plans",
                "GET/v1/glass/billing/invoices": "View Invoices",
                "POST/v1/glass/billing/cycles/{month}": "Generate Invoices",
                "PUT/v1/glass/billing/invoices/{id}/published": "Publish Invoice",
                "GET/v1/glass/billing/invoices/{id}": "View Invoice Detail",
                "GET/v1/glass/billing/invoices/{id}/usage": "View Invoice Usage",
                "POST/v1/glass/billing/invoices/{id}/adjustments": "Create Adjustment",
                "PUT/v1/glass/billing/invoices/{invoice_id}/adjustments/{id}": "Modify Adjustment",  # noqa
                "DELETE/v1/glass/billing/invoices/{invoice_id}/adjustments/{id}": "Delete Adjustment",  # noqa
                "POST/v1/glass/sim/allocations/{allocation_reference}/account/{account_id}/rateplan/{rateplan_id}/custom": "Create Allocation",  # noqa
                "GET/v1/glass/sim/allocations": "View Allocations",
                "POST/v1/glass/sim/documents": "Create Range",
                "GET/v1/glass/sim/ranges": "View Ranges",
                "GET/v1/glass/sim/cards/usage/export": "Export SIM Information",
                "GET/v1/glass/sim/connection/history/export": "Export Connection History",  # noqa
                "GET/v1/glass/sim/sms/connection/history/export": "Export SMS Connection History",  # noqa
                "GET/v1/glass/sim/voice/connection/history/export": "Export Voice Connection History",  # noqa
                "GET/v1/glass/billing/invoices/{id}/usage/export": "Export Invoice Usage",  # noqa
                "GET/v1/glass/market/accounts": "View Overall Market Share",  # noqa
                "GET/v1/glass/market/accounts/imsi": "View Market Share For SIM",  # noqa
                "GET/v1/glass/market/account/{account_id}": "View Market Share For Account",  # noqa
                "GET/v1/glass/audit/{account_id}": "Account Audit Logs",
                "GET/v1/glass/sim/cards/audit": "SIM Audit Logs",
            }
            has_apis = {
                "GET/v1/glass/sim/cards/status/{imsi}": "View SIM Status",
                "POST/v1/glass/sim/cards/activate": "SIM Activate",
                "POST/v1/glass/sim/cards/deactivate": "SIM Deactivate",
            }
            has_apis_proper_names = {
                "GET/v1/glass/sim/cards/status/{imsi}": "View SIM Status API",
                "POST/v1/glass/sim/cards/activate": "SIM Activate API",
                "POST/v1/glass/sim/cards/deactivate": "SIM Deactivate API",
            }
            for resource, scopes in resources_to_search.items():
                # Step 4.1: Scope creation.
                for scope in scopes:
                    proper_name = proper_names.get(f"{scope}")
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                        f"resource-server/scope"
                    )
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }
                    data = {
                        "name": f"{scope}",
                        "displayName": f"{proper_name}",
                    }
                    response = requests.post(
                        url, headers=headers, json=data, timeout=settings.TIMEOUT
                    )
                    if response.status_code == 201:
                        logging.info(f"Scope {resource} created.")
                    if response.status_code != 201 and response.status_code != 409:
                        raise MigrationError()

                # Step 4.2: Resource and associated Scope creation.
                url = f"{BASE_URL}/authz/protection/resource_set"
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}",
                }
                data = {
                    "name": f"{resource}",
                    "displayName": f"{resource}",
                    "resource_scopes": scopes,
                    "owner": f"{CLIENT_ID}",
                }
                response = requests.post(
                    url, headers=headers, json=data, timeout=settings.TIMEOUT
                )
                if response.status_code == 201:
                    logging.info(f"Resource {resource} created.")
                if response.status_code != 201 and response.status_code != 409:
                    raise MigrationError()

            # Step 5: Permission creation.
            role_policy_list = []
            for role_name in roles_to_search:
                policy_response_data = []
                url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}"
                    f"/authz/resource-server/policy?"
                    f"first=0&max=10&name={role_name}&permission=false"
                )
                headers = {"Authorization": f"Bearer {access_token}"}

                policy_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                policy_response_data = policy_response.json()

                if len(policy_response_data) != 0 and "id" in policy_response_data[0]:
                    policy_ids = [policy["id"] for policy in policy_response_data]
                    role_policy_list.extend(policy_ids)

            for resource, scopes in resources_to_search.items():
                resource_response_data = []
                url = (
                    f"{BASE_ADMIN_URL}/clients/"
                    f"{CLIENT_ID}/authz/resource-server/resource?"
                    f"deep=false&first=0&max=20&name={resource}"
                )
                headers = {"Authorization": f"Bearer {access_token}"}

                resource_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                resource_response_data = resource_response.json()
                resource_id = resource_response_data[0]["_id"]

                for scope in scopes:
                    scope_response_data = []
                    url = (
                        f"{BASE_ADMIN_URL}/clients/"
                        f"{CLIENT_ID}/authz/resource-server/scope?"
                        f"deep=false&first=0&max=20&name={scope}"
                    )
                    headers = {"Authorization": f"Bearer {access_token}"}

                    scope_response = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )
                    scope_response_data = scope_response.json()
                    scope_id = scope_response_data[0]["id"]

                    proper_name = proper_names.get(f"{scope}")
                    has_api = has_apis.get(f"{scope}")
                    has_apis_proper_name = has_apis_proper_names.get(f"{scope}")

                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}"
                        f"/authz/resource-server/permission/scope"
                    )
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }
                    if has_api:
                        data = {
                            "type": "scope",
                            "logic": "POSITIVE",
                            "decisionStrategy": "AFFIRMATIVE",
                            "name": f"{proper_name}",
                            "description": f"{proper_name}",
                            "resources": [f"{resource_id}"],
                            "scopes": [f"{scope_id}"],
                            "policies": role_policy_list,
                        }
                        response = requests.post(
                            url, headers=headers, json=data, timeout=settings.TIMEOUT
                        )
                        if response.status_code != 201 and response.status_code != 409:
                            raise MigrationError()

                        data = {
                            "type": "scope",
                            "logic": "POSITIVE",
                            "decisionStrategy": "AFFIRMATIVE",
                            "name": f"{has_apis_proper_name}",
                            "description": f"{has_api}",
                            "resources": [f"{resource_id}"],
                            "scopes": [f"{scope_id}"],
                            "policies": role_policy_list,
                        }
                        response = requests.post(
                            url, headers=headers, json=data, timeout=settings.TIMEOUT
                        )
                        if response.status_code != 201 and response.status_code != 409:
                            raise MigrationError()
                    else:
                        data = {
                            "type": "scope",
                            "logic": "POSITIVE",
                            "decisionStrategy": "AFFIRMATIVE",
                            "name": f"{proper_name}",
                            "description": f"{proper_name}",
                            "resources": [f"{resource_id}"],
                            "scopes": [f"{scope_id}"],
                            "policies": role_policy_list,
                        }
                        response = requests.post(
                            url, headers=headers, json=data, timeout=settings.TIMEOUT
                        )
                        if response.status_code != 201 and response.status_code != 409:
                            raise MigrationError()

            return True
        except Exception as e:
            raise MigrationError(str(e))


if __name__ == "__main__":
    migration = MigrationService()
    migration.create_migration()
