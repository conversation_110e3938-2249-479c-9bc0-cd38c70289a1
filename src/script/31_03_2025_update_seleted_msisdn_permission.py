import logging
import shlex
import subprocess  # nosec

import requests

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class MigrationService:
    def __init__(self):
        ...

    def create_migration(
        self,
    ) -> bool:
        try:
            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
            CLIENT_ID = settings.KEYCLOAK_CLIENT_ID

            logging.info(f"Base admin url:-{BASE_ADMIN_URL}")

            PERMISSION_NAME = "Update Multiple MSISDN Details"
            url = f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server/permission?name={PERMISSION_NAME}"  # noqa
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {access_token}",
            }
            response = requests.get(url, headers=headers, timeout=settings.TIMEOUT)
            permission_id = None
            if response.status_code == 200:
                permission_id = response.json()[0]["id"]
            else:
                logging.error(f"Failed to get permission ID: {response.status_code}")
                return False
            if permission_id is not None:
                url = f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server/policy/{permission_id}"  # noqa
                response = requests.delete(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                if response.status_code == 204:
                    logging.info(f"Permission {PERMISSION_NAME} deleted successfully.")
                else:
                    logging.error(
                        f"Failed to delete permission: {response.status_code}"
                    )
                    return False
            return True
        except requests.RequestException as e:
            logging.error(f"Request failed: {e}")
            return False
        except Exception as e:
            logging.error(f"An error occurred: {e}")
            return False

    def assign_permission(self) -> bool:
        try:
            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
            CLIENT_ID = settings.KEYCLOAK_CLIENT_ID

            policies_to_search = [
                "ClientAdmin",
            ]

            permission_to_remove = [
                "View Available MSISDN Count",
            ]

            headers = {"Authorization": f"Bearer {access_token}"}

            # Get Policy id by policy name
            policy_id = None
            for policy_name in policies_to_search:
                url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                    f"resource-server/policy?name={policy_name}"
                )

                policy_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                policy_response = policy_response.json()
                if len(policy_response) == 0:
                    raise Exception(f"Policy not found with name: {policy_name}")
                policy_id = policy_response[0]["id"]

            for permission_name in permission_to_remove:
                url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                    f"resource-server/permission?name={permission_name}"
                )
                permission_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )

                if (permission_response.status_code == 200) and (
                    len(permission_response.json()) == 1
                ):
                    permission_data = permission_response.json()[0]
                    permission_id = permission_data["id"]

                    # Getting Resource related to permission_id
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"
                        f"/policy/{permission_id}/resources"
                    )
                    resources = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )  # This will return list of resource for the given permission id
                    if resources.status_code == 200:
                        resource_list = resources.json()
                        resource_ids = []
                        for resource in resource_list:
                            resource_ids.append(resource["_id"])

                    # Getting scopes related to permission_id
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"
                        f"/policy/{permission_id}/scopes"
                    )
                    scopes = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )  # This will return list of scopes for the given permission id
                    if scopes.status_code == 200:
                        scope_list = scopes.json()
                        scope_ids = []
                        for scope in scope_list:
                            scope_ids.append(scope["id"])

                    # Getting Policies related to permission_id
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"
                        f"/policy/{permission_id}/associatedPolicies"
                    )
                    policies = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )  # This will return list of policy for the given permission id
                    if policies.status_code == 200:
                        policy_list = policies.json()
                        permission_policy_ids = []
                        for policy in policy_list:
                            permission_policy_ids.append(policy["id"])
                        if policy_id not in permission_policy_ids:
                            permission_policy_ids.append(policy_id)

                            # Adding policy to permission
                            url = (
                                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                                f"resource-server/permission/scope/{permission_id}"
                            )
                            data = {
                                "id": permission_id,
                                "name": permission_data["name"],
                                "description": permission_data["description"],
                                "type": "scope",
                                "logic": "POSITIVE",
                                "decisionStrategy": "AFFIRMATIVE",
                                "resources": resource_ids,
                                "scopes": scope_ids,
                                "policies": permission_policy_ids,
                            }
                            response = requests.put(
                                url,
                                headers=headers,
                                json=data,
                                timeout=settings.TIMEOUT,
                            )
                            if response.status_code != 201:
                                raise Exception()
                            logging.info(f"{permission_name} added to ClientAdmin")
            return True
        except Exception as e:
            raise Exception(str(e))


# List of scripts to run
script = [
    'python -m script.scope_permission_creation_dynamic_script \
    --roles DistributorAdmin ClientAdmin \
    --grouped_resources "SIM Details" "PUT/v1/glass/sim/msisdn" \
    "Update Selected MSISDN Records"',
    'python -m script.scope_permission_creation_dynamic_script \
    --roles DistributorAdmin ClientAdmin \
    --grouped_resources "SIM Details" \
    "PUT/v1/glass/sim/msisdn/profile/{sim_profile}/factor/{msisdn_factor}/documents" \
    "Update Multiple MSISDN Records"',
]


# Function to run a script
def run_script(command):
    args = shlex.split(command)
    subprocess.run(args, check=True)  # nosec


if __name__ == "__main__":
    migration = MigrationService()

    migration.create_migration()
    migration.assign_permission()
    for script in script:
        run_script(script)
        print(f"Running script: {script}")


"""python -m script.31_03_2025_update_seleted_msisdn_permission"""
