import argparse
import logging

import requests

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class MigrationError(BaseException):
    ...


class MigrationService:
    def __init__(self, policies_to_search: list[str], permissions_to_remove: list[str]):
        self.policies_to_search = policies_to_search
        self.permissions_to_remove = permissions_to_remove
        self.base_admin_url = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
        self.client_id = settings.KEYCLOAK_CLIENT_ID
        self.headers = {"Authorization": f"Bearer {access_token}"}

    def create_migration(self) -> bool:
        try:
            policy_ids = self.get_policy_ids()
            if not policy_ids:
                raise MigrationError("No valid policies found.")

            for permission_name in self.permissions_to_remove:
                self.remove_permission_from_policies(permission_name, policy_ids)

            return True
        except Exception as e:
            raise MigrationError(str(e))

    def get_policy_ids(self) -> dict:
        policy_ids = {}
        for policy_name in self.policies_to_search:
            url = (
                f"{self.base_admin_url}/clients/{self.client_id}/authz/"
                f"resource-server/policy?name={policy_name}"
            )
            response = requests.get(
                url, headers=self.headers, timeout=settings.TIMEOUT
            ).json()
            if response:
                policy_ids[policy_name] = response[0]["id"]
            else:
                logging.warning(f"Policy not found: {policy_name}")

        return policy_ids

    def remove_permission_from_policies(self, permission_name: str, policy_ids: dict):
        url = f"{self.base_admin_url}/clients/{self.client_id}/authz/resource-server/permission?name={permission_name}"  # noqa
        response = requests.get(url, headers=self.headers, timeout=settings.TIMEOUT)

        if response.status_code != 200 or not response.json():
            logging.warning(f"Permission '{permission_name}' not found.")
            return

        permission_data = response.json()[0]
        permission_id = permission_data["id"]

        resource_ids = self.get_related_ids(permission_id, "resources", "_id")
        scope_ids = self.get_related_ids(permission_id, "scopes", "id")
        permission_policy_ids = self.get_related_ids(
            permission_id, "associatedPolicies", "id"
        )

        removed_from = []
        for policy_name, policy_id in policy_ids.items():
            if policy_id in permission_policy_ids:
                permission_policy_ids.remove(policy_id)
                self.update_permission(
                    permission_id,
                    permission_data,
                    resource_ids,
                    scope_ids,
                    permission_policy_ids,
                )
                removed_from.append(policy_name)

        if removed_from:
            logging.info(f"Removed {permission_name} from: {', '.join(removed_from)}")
        else:
            logging.warning(
                f"{permission_name} was not found in any specified policies."
            )

    def get_related_ids(self, permission_id: str, endpoint: str, key: str) -> list:
        url = f"{self.base_admin_url}/clients/{self.client_id}/authz/resource-server/policy/{permission_id}/{endpoint}"  # noqa
        response = requests.get(url, headers=self.headers, timeout=settings.TIMEOUT)
        return (
            [item[key] for item in response.json()]
            if response.status_code == 200
            else []
        )

    def update_permission(
        self,
        permission_id: str,
        permission_data: dict,
        resource_ids: list,
        scope_ids: list,
        permission_policy_ids: list,
    ):
        url = f"{self.base_admin_url}/clients/{self.client_id}/authz/resource-server/permission/scope/{permission_id}"  # noqa
        data = {
            "id": permission_id,
            "name": permission_data["name"],
            "description": permission_data.get("description", ""),
            "type": "scope",
            "logic": "POSITIVE",
            "decisionStrategy": "AFFIRMATIVE",
            "resources": resource_ids,
            "scopes": scope_ids,
            "policies": permission_policy_ids,
        }
        response = requests.put(
            url, headers=self.headers, json=data, timeout=settings.TIMEOUT
        )
        if response.status_code != 201:
            raise MigrationError(
                f"Failed to update permission {permission_data['name']}"
            )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Remove permissions from Keycloak policies"
    )
    parser.add_argument(
        "--policies", nargs="+", required=True, help="List of policy names"
    )
    parser.add_argument(
        "--permissions", nargs="+", required=True, help="List of permissions to remove"
    )

    args = parser.parse_args()

    migration = MigrationService(args.policies, args.permissions)
    migration.create_migration()
    logging.info("Successfully removed specified permissions from the given policies.")


# python -m script.04_04_2025_remove_permissions --policies ClientAdmin DistributorAdmin --permissions "Update Multiple MSISDN Records" # noqa
