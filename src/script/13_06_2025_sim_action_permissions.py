import logging
import runpy
import sys


def run_scope_permission_script():
    try:
        sys.argv = [
            "scope_permission_creation_dynamic_script",
            "--roles",
            "ClientAdmin",
            "DistributorAdmin",
            "--grouped_resources",
            "SIM Details",
            "POST/v1/glass/sim/cards/flush",
            "Send Cancel Location",
            "--grouped_resources",
            "SIM Details",
            "POST/v1/glass/sim/cards/SMS",
            "Send SMS",
            "--grouped_resources",
            "SIM Details",
            "POST/v1/glass/sim/cards/APN",
            "Update APN",
            "--grouped_resources",
            "SIM Details",
            "POST/v1/glass/sim/cards/POD",
            "Disconnect POD",
        ]

        runpy.run_module(
            "script.scope_permission_creation_dynamic_script", run_name="__main__"
        )
    except Exception as e:
        logging.error(f"Error: {e}")


if __name__ == "__main__":
    run_scope_permission_script()


"""
cd src/
run this command:- python -m script.13_06_2026_sim_action_permissions

"""
