import logging
from uuid import UUID

import requests
from pydantic import BaseModel

from app.common import get_access_token
from app.core.config import settings

BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
access_token = get_access_token()

logger = logging.getLogger(__name__)


class MigrationError(BaseException):
    ...


class Groups(BaseModel):
    id: UUID
    name: str
    path: str
    subGroups: list["Groups"] = []


class GroupRole(BaseModel):
    id: UUID
    name: str
    description: str | None = None
    composite: bool
    clientRole: bool
    containerId: str


class MigrationService:
    def __init__(
        self,
    ):
        ...

    def assign_roles_to_accounts(self) -> bool:
        try:
            NextgenGroup = self._get_groups()
            roles = self._get_roles()
            for subGroup in NextgenGroup["subGroups"]:
                self._assign_roles_to_group(subGroup, roles)
            return True
        except Exception as e:
            raise MigrationError(str(e))

    def _assign_roles_to_group(self, subGroup, roles):
        url = f"{BASE_ADMIN_URL}/groups/{subGroup['id']}/role-mappings/realm"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {access_token}",
        }

        for role in roles:
            data = [{"id": f"{role['id']}", "name": f"{role['name']}"}]
            response = requests.post(
                url, headers=headers, json=data, timeout=settings.TIMEOUT
            )
            if response.status_code != 201 and response.status_code != 204:
                raise MigrationError()

    def unassign_roles(self):
        logger.info("Parent function of unassign role...")
        group_list = self._get_group_list()
        logger.info(f"group list {group_list}")
        for groups in group_list:
            for subgroup in groups.subGroups:
                group_roles = self._get_group_roles(subgroup.id)
                self._unassign_roles_from_group(subgroup, group_roles)

    def _unassign_roles_from_group(self, subGroup: Groups, roles: list[GroupRole]):
        url = f"{BASE_ADMIN_URL}/groups/{subGroup.id}/role-mappings/realm"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {access_token}",
        }
        for role in roles:
            data = [
                {
                    "id": f"{role.id}",
                    "name": f"{role.name}",
                    "description": f"{role.description}",
                    "composite": f"{role.composite}",
                    "clientRole": f"{role.clientRole}",
                    "containerId": f"{role.containerId}",
                }
            ]
            response = requests.delete(
                url, headers=headers, json=data, timeout=settings.TIMEOUT
            )
            logger.info(f"Unassigned role status - {response.status_code}")
            logger.info(f"Role {role.name} unassigned from group {subGroup.name}")

    def _get_group_roles(self, subgroup_id) -> list[GroupRole]:
        url = f"{BASE_ADMIN_URL}/groups/{subgroup_id}/role-mappings/realm/composite"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {access_token}",
        }
        response = requests.get(url, headers=headers, timeout=settings.TIMEOUT).json()
        return [GroupRole(**item) for item in response]

    def _get_group_list(self) -> list[Groups]:
        url = f"{BASE_ADMIN_URL}/groups"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {access_token}",
        }
        response = requests.get(url, headers=headers, timeout=settings.TIMEOUT).json()
        return [Groups(**groups) for groups in response]

    def _get_roles(self):
        roles_to_search = ["ClientAdmin", "ClientUser"]
        role_response_data = []
        for role_name in roles_to_search:

            url = f"{BASE_ADMIN_URL}/roles?search={role_name}"
            headers = {"Authorization": f"Bearer {access_token}"}
            role_response = requests.get(url, headers=headers, timeout=settings.TIMEOUT)

            roles_data = role_response.json()

            if roles_data:
                role_response_data.extend(roles_data)

        return role_response_data

    def _get_groups(self):
        group_path = "Nextgen Clearing"
        url = f"{BASE_ADMIN_URL}/group-by-path/{group_path}"
        headers = {"Authorization": f"Bearer {access_token}"}
        groups_data = requests.get(url, headers=headers, timeout=settings.TIMEOUT)
        return groups_data.json()


if __name__ == "__main__":
    migration = MigrationService()
    logger.info("Role unassignment started")
    migration.unassign_roles()
    logger.info("Role unassignment completed")
    # migration.assign_roles_to_accounts()
