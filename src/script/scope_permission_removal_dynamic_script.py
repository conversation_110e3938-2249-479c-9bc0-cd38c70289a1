import argparse
import logging
from collections import defaultdict

import requests

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class RemovalError(BaseException):
    ...


class RemovalService:
    def __init__(self):
        ...

    def remove_scopes_and_permissions(
        self, roles_to_search, resources_to_search, proper_names
    ) -> bool:
        try:
            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
            CLIENT_ID = settings.KEYCLOAK_CLIENT_ID

            logging.info(f"Roles to search: {roles_to_search}")
            logging.info(f"Resources to search: {resources_to_search}")
            logging.info(f"Proper names: {proper_names}")

            role_policy_ids = []
            for role_name in roles_to_search:
                url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                    f"resource-server/policy?name={role_name}&permission=false"
                )
                headers = {"Authorization": f"Bearer {access_token}"}
                response = requests.get(url, headers=headers, timeout=settings.TIMEOUT)
                policies = response.json()
                role_policy_ids.extend([p["id"] for p in policies if "id" in p])

            for resource, scopes in resources_to_search.items():
                scopes_to_remove = set(scopes)

                # Step 1: Unassign all specified scopes from the resource
                resource_lookup_url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                    f"resource-server/resource?name={resource}"
                )
                headers = {"Authorization": f"Bearer {access_token}"}
                resource_resp = requests.get(
                    resource_lookup_url,
                    headers=headers,
                    timeout=settings.TIMEOUT,
                )
                resource_list = resource_resp.json()
                if not resource_list:
                    logging.warning(
                        f"Resource '{resource}' not found. Skipping unassignment."
                    )
                else:
                    resource_id = resource_list[0]["_id"]
                    resource_details_url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                        f"resource-server/resource/{resource_id}"
                    )
                    resource_data = requests.get(
                        resource_details_url,
                        headers=headers,
                        timeout=settings.TIMEOUT,
                    ).json()

                    existing_scopes = resource_data.get("scopes", [])
                    updated_scopes = [
                        {"id": s["id"], "name": s["name"]}
                        for s in existing_scopes
                        if s["name"] not in scopes_to_remove
                    ]

                    update_payload = {
                        "name": resource_data["name"],
                        "owner": resource_data["owner"],
                        "ownerManagedAccess": resource_data.get(
                            "ownerManagedAccess", False
                        ),
                        "displayName": resource_data.get("displayName", ""),
                        "attributes": resource_data.get("attributes", {}),
                        "_id": resource_id,
                        "uris": resource_data.get("uris", []),
                        "scopes": updated_scopes,
                        "icon_uri": resource_data.get("icon_uri", ""),
                        "type": resource_data.get("type", ""),
                    }

                    update_resp = requests.put(
                        resource_details_url,
                        headers={**headers, "Content-Type": "application/json"},
                        json=update_payload,
                        timeout=settings.TIMEOUT,
                    )
                    if update_resp.status_code != 204:
                        raise RemovalError(
                            f"Failed to unassign scopes from resource '{resource}'"
                        )
                    else:
                        logging.info(
                            f"Unassigned scopes {list(scopes_to_remove)}"
                            f" from resource '{resource}'"
                        )

                # Step 2: Delete permissions
                for scope in scopes:
                    proper_name = proper_names.get(scope)
                    perm_url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                        f"resource-server/permission/scope?name={proper_name}"
                    )
                    perm_response = requests.get(
                        perm_url, headers=headers, timeout=settings.TIMEOUT
                    )
                    permissions = perm_response.json()

                    for permission in permissions:
                        if "id" in permission:
                            del_url = (
                                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                                f"resource-server/permission/{permission['id']}"
                            )
                            del_response = requests.delete(
                                del_url, headers=headers, timeout=settings.TIMEOUT
                            )
                            if del_response.status_code == 204:
                                logging.info(f"Deleted permission: {proper_name}")
                            elif del_response.status_code != 404:
                                raise RemovalError(
                                    f"Failed to delete permission {proper_name}"
                                )

                    # Step 3: Delete scopes
                    scope_url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                        f"resource-server/scope?name={scope}"
                    )
                    scope_response = requests.get(
                        scope_url, headers=headers, timeout=settings.TIMEOUT
                    )
                    scopes_data = scope_response.json()

                    for s in scopes_data:
                        if "id" in s:
                            del_scope_url = (
                                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                                f"resource-server/scope/{s['id']}"
                            )
                            del_scope_response = requests.delete(
                                del_scope_url,
                                headers=headers,
                                timeout=settings.TIMEOUT,
                            )
                            if del_scope_response.status_code == 204:
                                logging.info(f"Deleted scope: {scope}")
                            elif del_scope_response.status_code != 404:
                                raise RemovalError(f"Failed to delete scope {scope}")

            return True

        except Exception as e:
            raise RemovalError(str(e))

    @staticmethod
    def parse_arguments():
        parser = argparse.ArgumentParser(
            description="Keycloak Scope & Permission Removal Script"
        )
        parser.add_argument(
            "--roles",
            type=str,
            nargs="+",
            default=[],
            help="List of roles to search for",
        )
        parser.add_argument(
            "--grouped_resources",
            type=str,
            nargs=3,
            action="append",
            help=(
                "Provide group name, resource, and proper name in format: "
                "'group_name resource proper_name'."
            ),
        )
        return parser.parse_args()

    @staticmethod
    def process_grouped_resources(grouped_resources):
        resources_to_search = defaultdict(list)
        proper_names = {}

        for group_data in grouped_resources:
            group, resource, proper_name = group_data
            resources_to_search[group].append(resource)
            proper_names[resource] = proper_name

        return resources_to_search, proper_names


if __name__ == "__main__":
    remover = RemovalService()
    args = remover.parse_arguments()

    resources_to_search, proper_names = remover.process_grouped_resources(
        args.grouped_resources
    )

    remover.remove_scopes_and_permissions(args.roles, resources_to_search, proper_names)

"""
To run this script:

python -m script.scope_permission_removal_dynamic_script \
    --roles ClientAdmin Client_ReadOnly DistributorAdmin Distributor_ReadOnly \
    --grouped_resources 'Audit' 'GET/v1/auditlog/account-log' \
    'View Account Activity Log' \
    --grouped_resources 'Audit' 'GET/v1/auditlog/account-log/{id}' \
    'View Account Activity Details'
"""
