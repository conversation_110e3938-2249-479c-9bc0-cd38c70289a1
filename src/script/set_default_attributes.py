import logging

import requests

from app.common import get_access_token
from app.core.config import settings

BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
access_token = get_access_token()

logger = logging.getLogger(__name__)


class MigrationError(BaseException):
    ...


class SetDefaultRoleAttribute:
    def __init__(
        self,
    ):
        ...

    def set_default_role_attributes(self):
        try:
            roles_to_search = [
                "DistributorAdmin",
                "DistributorUser",
                "ClientAdmin",
                "ClientUser",
            ]
            for role_name in roles_to_search:
                role_response_data = []
                url = f"{BASE_ADMIN_URL}/roles?search={role_name}"
                headers = {"Authorization": f"Bearer {access_token}"}
                role_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                role_response_data = role_response.json()

                if len(role_response_data) != 0:
                    for role_data in role_response_data:
                        self.set_attributes_to_role(role_data)
        except Exception as e:
            raise MigrationError(str(e))

    def set_attributes_to_role(self, role_data):
        try:
            url = f"{BASE_ADMIN_URL}/roles-by-id/{role_data['id']}"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {access_token}",
            }
            if role_data["name"] == "DistributorAdmin":
                data = {
                    **role_data,
                    "attributes": {
                        "update_role": ["false"],
                        "delete_role": ["false"],
                    },
                }
            else:
                data = {
                    **role_data,
                    "attributes": {
                        "delete_role": ["false"],
                    },
                }
            response = requests.put(
                url, headers=headers, json=data, timeout=settings.TIMEOUT
            )
            logger.info(
                f"Role {role_data['name']} updated. response {response.status_code}"
            )
        except Exception as e:
            raise MigrationError(str(e))


if __name__ == "__main__":
    migration = SetDefaultRoleAttribute()
    migration.set_default_role_attributes()
    logger.info("Successfully updated role attributes.")
