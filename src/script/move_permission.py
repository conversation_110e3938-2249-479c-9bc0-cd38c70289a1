import argparse
import logging
from uuid import UUID

import requests
from pydantic import BaseModel

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class MigrationError(BaseException):
    ...


"""This script will move `View SIM Remains` from SIM Details to SIM Orders"""


class Scope(BaseModel):
    id: UUID
    name: str


class MigrationService:
    def __init__(
        self,
    ):
        ...

    def create_migration(
        self, from_resource: str, to_resource: str, scope_to_move: str, permission: str
    ) -> bool:
        try:

            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
            CLIENT_ID = settings.KEYCLOAK_CLIENT_ID

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {access_token}",
            }

            # Step 1 : Get Get SIM Remains scope
            get_scope_url = (
                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                f"resource-server/scope?name={scope_to_move}"
            )
            response = requests.get(
                get_scope_url, headers=headers, timeout=settings.TIMEOUT
            )
            if response.status_code != 200:
                raise MigrationError()
            scope_to_remove = response.json()[0]
            keys_to_remove = [
                key for key, value in scope_to_remove.items() if key == "displayName"
            ]
            # Remove the displayName
            for key in keys_to_remove:
                scope_to_remove.pop(key)

            # Step 2 : Get SIM Details resource
            get_resource_url = (
                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                f"resource-server/resource?name={from_resource}"
            )
            response = requests.get(
                get_resource_url, headers=headers, timeout=settings.TIMEOUT
            )
            if response.status_code != 200:
                raise MigrationError()
            sim_details_resource_id = response.json()[0]["_id"]

            # Step 3 : Get SIM Orders resource
            get_resource_url = (
                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                f"resource-server/resource?name={to_resource}"
            )
            response = requests.get(
                get_resource_url, headers=headers, timeout=settings.TIMEOUT
            )
            if response.status_code != 200:
                raise MigrationError()
            sim_orders_resource_id = response.json()[0]["_id"]

            # Step 4 : Remove SIM Remains permission from SIM Details resource
            get_resource_url = (
                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                f"resource-server/resource/{sim_details_resource_id}"
            )
            response = requests.get(
                get_resource_url, headers=headers, timeout=settings.TIMEOUT
            )
            if response.status_code != 200:
                raise MigrationError()

            logging.info(f"{scope_to_remove['name']} removed from {from_resource}")

            # Step 5 : Add SIM Remains permission to SIM Orders resource
            get_resource_url = (
                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                f"resource-server/resource/{sim_orders_resource_id}"
            )
            response = requests.get(
                get_resource_url, headers=headers, timeout=settings.TIMEOUT
            )
            if response.status_code != 200:
                raise MigrationError()
            logging.info(f"{scope_to_remove['name']} added to {to_resource}")

            # Step 6: Get permission
            permission_url = (
                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                f"resource-server/permission?name={permission}"
            )
            response = requests.get(
                permission_url, headers=headers, timeout=settings.TIMEOUT
            )
            if response.status_code != 200:
                raise MigrationError()
            permission_id = response.json()[0]["id"]

            # Step 7 : Get Associated Policy
            accociated_policy_url = (
                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                f"resource-server/policy/{permission_id}/associatedPolicies"
            )
            response = requests.get(
                accociated_policy_url, headers=headers, timeout=settings.TIMEOUT
            )
            if response.status_code != 200:
                raise MigrationError()
            policy_ids = [policy_id["id"] for policy_id in response.json()]

            # Step 8 : Delete permission View SIM Remains
            permission_url = (
                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                f"resource-server/permission/{permission_id}"
            )
            response = requests.delete(
                permission_url, headers=headers, timeout=settings.TIMEOUT
            )
            if response.status_code != 204:
                raise MigrationError()

            logging.info(f"{permission} removed.")

            # Step 9 : Create permission View SIM Remains
            permission_url = (
                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                f"resource-server/permission/scope"
            )
            permission_data = {}
            permission_data["type"] = "scope"
            permission_data["decisionStrategy"] = "AFFIRMATIVE"
            permission_data["description"] = permission
            permission_data["logic"] = "POSITIVE"
            permission_data["name"] = permission
            permission_data["policies"] = policy_ids
            permission_data["resources"] = [sim_orders_resource_id]
            permission_data["scopes"] = [scope_to_remove["id"]]

            response = requests.post(
                permission_url,
                json=permission_data,
                headers=headers,
                timeout=settings.TIMEOUT,
            )
            if response.status_code != 201:
                raise MigrationError()
            logging.info(f"{permission} created in resource {to_resource}.")

        except Exception as e:
            raise MigrationError(str(e))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Move permission from one resource to other"
    )
    parser.add_argument("--from_resource", type=str, required=True)
    parser.add_argument("--to_resource", type=str, required=True)
    parser.add_argument("--scope_to_move", type=str, required=True)
    parser.add_argument("--permission", type=str, required=True)

    args = parser.parse_args()

    migration = MigrationService()
    migration.create_migration(
        args.from_resource, args.to_resource, args.scope_to_move, args.permission
    )
    logging.info("Done")

# run this script using command below
# python -m script.move_permission --from_resource 'SIM Details' --to_resource 'SIM Orders' --scope_to_move 'GET/v1/glass/sim/cards/remains' --permission 'View SIM Remains' # noqa
