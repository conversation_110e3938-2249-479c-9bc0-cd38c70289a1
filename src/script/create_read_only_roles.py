import logging

import requests

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class MigrationError(BaseException):
    ...


class MigrationService:
    def __init__(
        self,
    ):
        ...

    def create_migration(self) -> bool:
        try:

            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
            CLIENT_ID = settings.KEYCLOAK_CLIENT_ID

            # Step 2: Based on default role policy creation.

            roles_to_search = [
                "Distributor_ReadOnly",
                "Client_ReadOnly",
            ]

            policies_to_search = [
                "Distributor_ReadOnly",
                "Client_ReadOnly",
            ]

            permissions = {
                "Distributor_ReadOnly": [
                    # Accounts
                    "View Account Details",
                    "View Accounts",
                    # SIM Details
                    "View SIM Information",
                    "View SIM Status",
                    "View Account Names",
                    "View SIM Remains",
                    "View SIM Connection Summary",
                    "View Connection History",
                    "View SMS Connection History",
                    "View Voice Connection History",
                    # Users
                    "View Roles",
                    "View Permission By Role",
                    # Rate Plan
                    "View Rate Plan",
                    "View Rate Plans",
                    "Account Rate Plans",
                    # Billing
                    "View Invoices",
                    "View Invoice Detail",
                    "View Invoice Usage",
                    # SIM Orders
                    "View Allocations",
                    "View Ranges",
                    # Reporting
                    "Export SIM Information",
                    "Export Connection History",
                    "Export SMS Connection History",
                    "Export Voice Connection History",
                    "Export Invoice Usage",
                    "View Market Share For SIM",
                    "View Market Share For Account",
                    "View Overall Market Share",
                    # Audit
                    "SIM Audit Logs",
                    "Account Audit Logs",
                ],
                "Client_ReadOnly": [
                    # Accounts
                    "View Account Details",
                    # SIM Details
                    "View SIM Information",
                    "View SIM Status",
                    "View Account Names",
                    "View SIM Connection Summary",
                    "View Connection History",
                    "View SMS Connection History",
                    "View Voice Connection History",
                    # Users
                    "View Roles",
                    "View Permission By Role",
                    # Rate Plan
                    "View Rate Plan",
                    "View Rate Plans",
                    "View Client Rate Plans",
                    # Billing
                    "View Invoices",
                    "View Invoice Detail",
                    "View Invoice Usage",
                    # Reporting
                    "Export SIM Information",
                    "Export Connection History",
                    "Export SMS Connection History",
                    "Export Voice Connection History",
                    "Export Invoice Usage",
                    "View Market Share For SIM",
                    "View Market Share For Account",
                    # Audit
                    "SIM Audit Logs",
                ],
            }

            for role_name in roles_to_search:
                role_response_data = []
                url = f"{BASE_ADMIN_URL}/roles?search={role_name}"
                headers = {"Authorization": f"Bearer {access_token}"}

                role_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                role_response_data = role_response.json()

                if len(role_response_data) == 0:
                    logging.info(f"Role {role_name} not found.")
                    # import pdb; pdb.set_trace()
                    create_role_url = f"{BASE_ADMIN_URL}/roles"
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }

                    data = {
                        "name": f"{role_name}",
                        # "description": f"{role_name}",
                    }
                    response = requests.post(
                        create_role_url,
                        headers=headers,
                        json=data,
                        timeout=settings.TIMEOUT,
                    )
                    if response.status_code != 201 and response.status_code != 409:
                        raise MigrationError()
                    logging.info(f"Role {role_name} created.")

            for policy_name in policies_to_search:
                role_response_data = []
                url = f"{BASE_ADMIN_URL}/roles?search={policy_name}"
                headers = {"Authorization": f"Bearer {access_token}"}

                role_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                role_response_data = role_response.json()

                if len(role_response_data) != 0:
                    role_id = role_response_data[0]["id"]

                    # Step 3: Policy create.
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                        f"resource-server/policy/role"
                    )
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }

                    data = {
                        "name": f"{policy_name}",
                        "description": f"{policy_name}",
                        "type": "role",
                        "logic": "POSITIVE",
                        "decisionStrategy": "UNANIMOUS",
                        "roles": [{"id": f"{role_id}"}],
                    }
                    response = requests.post(
                        url, headers=headers, json=data, timeout=settings.TIMEOUT
                    )
                    if response.status_code != 201 and response.status_code != 409:
                        raise MigrationError()
                    logging.info(f"Policy {policy_name} created.")

            # Get Policy id by policy name
            for policy_name, (role, permission_to_assign) in zip(
                policies_to_search, permissions.items()
            ):
                policy_id = None
                # for policy_name in policies_to_search:
                url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                    f"resource-server/policy?name={policy_name}"
                )

                policy_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                policy_response = policy_response.json()
                if len(policy_response) == 0:
                    raise MigrationError(f"Policy not found with name: {policy_name}")
                policy_id = policy_response[0]["id"]

                for permission_name in permission_to_assign:
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                        f"resource-server/permission?name={permission_name}"
                    )
                    permission_response = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )

                    if permission_response.status_code == 200:
                        permission_data = permission_response.json()[0]
                        permission_id = permission_data["id"]

                        # Getting Resource related to permission_id
                        url = (
                            f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"  # noqa
                            f"/policy/{permission_id}/resources"
                        )
                        resources = requests.get(
                            url, headers=headers, timeout=settings.TIMEOUT
                        )  # This will return list of resource for the given permission id # noqa
                        if resources.status_code == 200:
                            resource_list = resources.json()
                            resource_ids = []
                            for resource in resource_list:
                                resource_ids.append(resource["_id"])

                        # Getting scopes related to permission_id
                        url = (
                            f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"  # noqa
                            f"/policy/{permission_id}/scopes"
                        )
                        scopes = requests.get(
                            url, headers=headers, timeout=settings.TIMEOUT
                        )  # This will return list of scopes for the given permission id
                        if scopes.status_code == 200:
                            scope_list = scopes.json()
                            scope_ids = []
                            for scope in scope_list:
                                scope_ids.append(scope["id"])

                        # Getting Policies related to permission_id
                        url = (
                            f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"  # noqa
                            f"/policy/{permission_id}/associatedPolicies"
                        )
                        policies = requests.get(
                            url, headers=headers, timeout=settings.TIMEOUT
                        )  # This will return list of policy for the given permission id
                        if policies.status_code == 200:
                            policy_list = policies.json()
                            permission_policy_ids = []
                            for policy in policy_list:
                                permission_policy_ids.append(policy["id"])
                            if policy_id not in permission_policy_ids:
                                permission_policy_ids.append(policy_id)

                                # Adding policy to the permission
                                url = (
                                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                                    f"resource-server/permission/scope/{permission_id}"
                                )
                                data = {
                                    "id": permission_id,
                                    "name": permission_data["name"],
                                    "description": permission_data["description"],
                                    "type": "scope",
                                    "logic": "POSITIVE",
                                    "decisionStrategy": "AFFIRMATIVE",
                                    "resources": resource_ids,
                                    "scopes": scope_ids,
                                    "policies": permission_policy_ids,
                                }
                                response = requests.put(
                                    url,
                                    headers=headers,
                                    json=data,
                                    timeout=settings.TIMEOUT,
                                )
                                if response.status_code != 201:
                                    raise MigrationError()
                                logging.info(
                                    f"{permission_name} added to {policy_name}"
                                )
            return True
        except Exception as e:
            raise MigrationError(str(e))


if __name__ == "__main__":
    migration = MigrationService()
    migration.create_migration()
    logging.info("Done")
