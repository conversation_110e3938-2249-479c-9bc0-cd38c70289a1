import logging

import requests

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class MigrationError(BaseException):
    ...


# This migration will create `Automation rules` resource, scopes, and permissions
# This migration script will assign permissions to AutomationRule and
# DistributorAdmin only.
# It is intended to be run after the migration script has been run.
class MigrationService:
    def __init__(
        self,
    ):
        ...

    def create_migration(self) -> bool:
        try:
            BASE_URL = f"{settings.BASE_URL}{settings.KEYCLOAK_REALM}"
            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
            CLIENT_ID = settings.KEYCLOAK_CLIENT_ID

            # Create Role & Policy
            roles_to_create = [
                "AutomationRule",
            ]

            policies_to_create = [
                "AutomationRule",
            ]

            for role_name in roles_to_create:
                role_response_data = []
                url = f"{BASE_ADMIN_URL}/roles?search={role_name}"
                headers = {"Authorization": f"Bearer {access_token}"}

                role_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                role_response_data = role_response.json()

                if len(role_response_data) == 0:
                    logging.info(f"Role {role_name} not found.")

                    create_role_url = f"{BASE_ADMIN_URL}/roles"
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }

                    data = {
                        "name": f"{role_name}",
                        "description": "SIM Automation permission to the portal",
                    }
                    response = requests.post(
                        create_role_url,
                        headers=headers,
                        json=data,
                        timeout=settings.TIMEOUT,
                    )
                    if response.status_code != 201 and response.status_code != 409:
                        raise MigrationError()
                    logging.info(f"Role {role_name} created.")

            for policy_name in policies_to_create:
                role_response_data = []
                url = f"{BASE_ADMIN_URL}/roles?search={policy_name}"
                headers = {"Authorization": f"Bearer {access_token}"}

                role_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                role_response_data = role_response.json()

                if len(role_response_data) != 0:
                    role_id = role_response_data[0]["id"]

                    # Step 3: Policy create.
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                        f"resource-server/policy/role"
                    )
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }

                    data = {
                        "name": f"{policy_name}",
                        "description": f"{policy_name}",
                        "type": "role",
                        "logic": "POSITIVE",
                        "decisionStrategy": "UNANIMOUS",
                        "roles": [{"id": f"{role_id}"}],
                    }
                    response = requests.post(
                        url, headers=headers, json=data, timeout=settings.TIMEOUT
                    )
                    if response.status_code != 201 and response.status_code != 409:
                        raise MigrationError()
                    logging.info(f"Policy {policy_name} created.")

            # ----------------------------------------------------

            policies_to_search = ["AutomationRule", "DistributorAdmin"]

            # Step 1: Resource and it's scope creation.
            resources_to_search = {
                "SIM Automation": [
                    "GET/v1/glass/rule",
                    "POST/v1/glass/rule",
                    "PUT/v1/glass/rule",
                    "GET/v1/glass/rule/{rule_uuid}",
                    "PATCH/v1/glass/rule/{rule_uuid}/{rule_status}",
                ],
            }
            proper_names = {
                "GET/v1/glass/rule": "View Rules",
                "POST/v1/glass/rule": "Create Rule",
                "PUT/v1/glass/rule": "Edit Rule",
                "GET/v1/glass/rule/{rule_uuid}": "View Rule Detail",
                "PATCH/v1/glass/rule/{rule_uuid}/{rule_status}": "Update Rule Status",
            }
            for resource, scopes in resources_to_search.items():
                # Step 1.1: Scope creation.
                for scope in scopes:
                    proper_name = proper_names.get(f"{scope}")
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                        f"resource-server/scope"
                    )
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }
                    data = {
                        "name": f"{scope}",
                        "displayName": f"{proper_name}",
                    }
                    response = requests.post(
                        url, headers=headers, json=data, timeout=settings.TIMEOUT
                    )
                    if response.status_code == 201:
                        logging.info(f"Scope {scope} created.")
                    if response.status_code != 201:
                        raise MigrationError(
                            f"Scope creation error. status_code: {response.status_code}"
                        )

                # Step 1.2: Resource and associated Scope creation.
                url = f"{BASE_URL}/authz/protection/resource_set"
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}",
                }
                data = {
                    "name": f"{resource}",
                    "displayName": f"{resource}",
                    "resource_scopes": scopes,
                    "owner": f"{CLIENT_ID}",
                }
                response = requests.post(
                    url, headers=headers, json=data, timeout=settings.TIMEOUT
                )
                if response.status_code == 201:
                    resource_id = response.json()["_id"]
                    logging.info(f"Resource {resource} created.")
                if response.status_code != 201:
                    raise MigrationError(
                        f"Resource creation error. status_code: {response.status_code}"
                    )

            # Step 2: Permission creation.
            role_policy_list = []
            for role_name in policies_to_search:
                policy_response_data = []
                url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}"
                    f"/authz/resource-server/policy?"
                    f"first=0&max=10&name={role_name}&permission=false"
                )
                headers = {"Authorization": f"Bearer {access_token}"}

                policy_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                policy_response_data = policy_response.json()

                if len(policy_response_data) != 0 and "id" in policy_response_data[0]:
                    policy_ids = [policy["id"] for policy in policy_response_data]
                    role_policy_list.extend(policy_ids)

            # Step 3: Assign resource, scope, and policies to permission
            for resource, scopes in resources_to_search.items():
                for scope in scopes:
                    scope_response_data = []
                    url = (
                        f"{BASE_ADMIN_URL}/clients/"
                        f"{CLIENT_ID}/authz/resource-server/scope?"
                        f"deep=false&first=0&max=20&name={scope}"
                    )
                    headers = {"Authorization": f"Bearer {access_token}"}

                    scope_response = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )
                    scope_response_data = scope_response.json()
                    scope_id = scope_response_data[0]["id"]

                    proper_name = proper_names.get(f"{scope}")

                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}"
                        f"/authz/resource-server/permission/scope"
                    )
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }
                    data = {
                        "type": "scope",
                        "logic": "POSITIVE",
                        "decisionStrategy": "AFFIRMATIVE",
                        "name": f"{proper_name}",
                        "description": f"{proper_name}",
                        "resources": [f"{resource_id}"],
                        "scopes": [f"{scope_id}"],
                        "policies": role_policy_list,
                    }
                    response = requests.post(
                        url, headers=headers, json=data, timeout=settings.TIMEOUT
                    )
                    if response.status_code != 201:
                        raise MigrationError(
                            "Permission assignment error."
                            f"status_code: {response.status_code}"
                        )
                    logging.info(f"Permission {proper_name} created inside {resource}.")

            return True
        except Exception as e:
            raise MigrationError(str(e))


if __name__ == "__main__":
    migration = MigrationService()
    migration.create_migration()
    logging.info("Successfully assigned permissions to AutomationRule.")

# To run this script you need to run this command
# python -m script.22_07_2024_create_automation_permissions
