import logging

import requests

from app.common import get_access_token
from app.core.config import settings

BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
access_token = get_access_token()

logger = logging.getLogger(__name__)


class LogoutError(BaseException):
    ...


class LogoutAllUsers:
    def __init__(self):
        ...

    def logout_all_users(self):
        # # This is for the new version for older version we need to pass paylaod
        # try:
        #     url = f"{BASE_ADMIN_URL}/logout-all"
        #     headers = {
        #         "Authorization": f"Bearer {access_token}",
        #         "Content-Type": "application/json",
        #     }
        #     response = requests.post(url, headers=headers, timeout=settings.TIMEOUT)
        #     if response.status_code == 200:
        #         logger.info("Successfully logged out all users.")
        #     else:
        #         logger.error(
        #             f"Failed to logout users. \
        #                 Status Code: {response.status_code}, \
        #                   Response: {response.text}")
        #         raise LogoutError(
        #             f"Logout failed with status code: {response.status_code}")
        # except Exception as e:
        #     raise LogoutError(str(e))

        # # This is for the old version and we need to pass paylaod

        try:
            url = f"{BASE_ADMIN_URL}/logout-all"
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
                "Accept": "application/json, text/plain, */*",
            }
            payload = {"realm": settings.KEYCLOAK_REALM}
            response = requests.post(
                url, headers=headers, json=payload, timeout=settings.TIMEOUT
            )
            if response.status_code == 200:
                logger.info("Successfully logged out all users.")
            else:
                logger.error(
                    f"Failed to logout users. \
                        Status Code: {response.status_code}, Response: {response.text}"
                )
                raise LogoutError(
                    f"Logout failed with status code: {response.status_code}"
                )
        except Exception as e:
            raise LogoutError(str(e))


if __name__ == "__main__":
    logout_script = LogoutAllUsers()
    logout_script.logout_all_users()
    logger.info("Logout all users script completed.")


# To run this script you need to run this command
# python -m script.29_04_2025_logout_all_active_users
