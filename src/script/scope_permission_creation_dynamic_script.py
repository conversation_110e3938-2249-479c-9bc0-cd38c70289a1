"""
24_01_2025

scope_permission_creation_dynamic_script_in_existing_resource
"""
import argparse
import logging
from collections import defaultdict

import requests

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class MigrationError(BaseException):
    ...


class MigrationService:
    def __init__(self):
        ...

    def create_migration(
        self, roles_to_search, resources_to_search, proper_names
    ) -> bool:
        try:
            BASE_URL = f"{settings.BASE_URL}{settings.KEYCLOAK_REALM}"
            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
            CLIENT_ID = settings.KEYCLOAK_CLIENT_ID

            logging.info(f"Roles to search:-{roles_to_search}")
            logging.info(f"Resources to search:-{resources_to_search}")
            logging.info(f"Proper names:-{proper_names}")
            logging.info(f"Base url:-{BASE_URL}")
            logging.info(f"Base admin url:-{BASE_ADMIN_URL}")

            for resource, scopes in resources_to_search.items():
                logging.info(f"Resources:-{resource}")
                logging.info(f"Scopes:-{scopes}")
                # Step 1.1: Scope creation
                for scope in scopes:
                    proper_name = proper_names.get(scope)
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}"
                        "/authz/resource-server/scope"
                    )
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }
                    data = {"name": scope, "displayName": proper_name}
                    response = requests.post(
                        url, headers=headers, json=data, timeout=settings.TIMEOUT
                    )
                    if response.status_code == 201:
                        logging.info(f"Scope {scope} created.")
                    if response.status_code not in [201, 409]:
                        raise MigrationError()

                # Step 1.2: Resource and associated Scope Updation.
                resource_response_data = []
                url = (
                    f"{BASE_ADMIN_URL}/clients/"
                    f"{CLIENT_ID}/authz/resource-server/resource?"
                    f"deep=false&first=0&max=20&name={resource}"
                )
                headers = {"Authorization": f"Bearer {access_token}"}

                logging.info(f"Scopes:-{scopes}")
                resource_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                resource_response_data = resource_response.json()

                if not resource_response_data:
                    logging.warning(f"Resource '{resource}' not found. Skipping...")
                    continue  # Skip to the next resource if not found.

                resource_id = resource_response_data[0]["_id"]

                # Step 1.3: Get Existing Scopes
                resource_scope_url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/"
                    f"authz/resource-server/resource/{resource_id}"
                )
                get_headers = {"Authorization": f"Bearer {access_token}"}
                resource_data = requests.get(
                    resource_scope_url, headers=get_headers, timeout=settings.TIMEOUT
                ).json()
                existing_scopes = [
                    scope["name"] for scope in resource_data.get("scopes", [])
                ]

                update_url = f"{BASE_URL}/authz/protection/resource_set/{resource_id}"
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}",
                }

                for scope in scopes:
                    existing_scopes.append(scope)
                data = {
                    "_id": f"{resource_id}",
                    "name": f"{resource}",
                    "resource_scopes": existing_scopes,
                }
                response = requests.put(
                    update_url, headers=headers, json=data, timeout=settings.TIMEOUT
                )
                if response.status_code == 204:
                    logging.info(f"Resource {resource} created.")
                if response.status_code != 204 and response.status_code != 409:
                    raise MigrationError()

            # Step 2: Permission creation.
            role_policy_list = []
            for role_name in roles_to_search:
                policy_response_data = []
                url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}"
                    f"/authz/resource-server/policy?"
                    f"first=0&max=10&name={role_name}&permission=false"
                )
                headers = {"Authorization": f"Bearer {access_token}"}

                policy_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                )
                policy_response_data = policy_response.json()

                if len(policy_response_data) != 0 and "id" in policy_response_data[0]:
                    policy_ids = [policy["id"] for policy in policy_response_data]
                    role_policy_list.extend(policy_ids)

            for resource, scopes in resources_to_search.items():
                for scope in scopes:
                    scope_response_data = []
                    url = (
                        f"{BASE_ADMIN_URL}/clients/"
                        f"{CLIENT_ID}/authz/resource-server/scope?"
                        f"deep=false&first=0&max=20&name={scope}"
                    )
                    headers = {"Authorization": f"Bearer {access_token}"}

                    scope_response = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )
                    scope_response_data = scope_response.json()
                    scope_id = scope_response_data[0]["id"]

                    proper_name = proper_names.get(f"{scope}")

                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}"
                        f"/authz/resource-server/permission/scope"
                    )
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}",
                    }
                    data = {
                        "type": "scope",
                        "logic": "POSITIVE",
                        "decisionStrategy": "AFFIRMATIVE",
                        "name": f"{proper_name}",
                        "description": f"{proper_name}",
                        "resources": [f"{resource_id}"],
                        "scopes": [f"{scope_id}"],
                        "policies": role_policy_list,
                    }
                    response = requests.post(
                        url, headers=headers, json=data, timeout=settings.TIMEOUT
                    )
                    if response.status_code != 201 and response.status_code != 409:
                        raise MigrationError()
                    logging.info(f"Permission {proper_name} created inside {resource}.")

            return True

        except Exception as e:
            raise MigrationError(str(e))

    @staticmethod
    def parse_arguments():
        parser = argparse.ArgumentParser(description="Keycloak Migration Script")
        parser.add_argument(
            "--roles",
            type=str,
            nargs="+",
            default=[],
            help="List of roles to search for",
        )
        parser.add_argument(
            "--grouped_resources",
            type=str,
            nargs=3,
            action="append",
            help=(
                "Provide group name, resource, and proper name in the format: "
                "'group_name resource proper_name'."
            ),
        )
        return parser.parse_args()

    @staticmethod
    def process_grouped_resources(grouped_resources):
        resources_to_search = defaultdict(list)
        proper_names = {}

        for group_data in grouped_resources:
            group, resource, proper_name = group_data
            resources_to_search[group].append(resource)
            proper_names[resource] = proper_name

        return resources_to_search, proper_names


if __name__ == "__main__":
    migration = MigrationService()
    args = migration.parse_arguments()

    # Process grouped resources into `resources_to_search` and `proper_names`
    resources_to_search, proper_names = migration.process_grouped_resources(
        args.grouped_resources
    )

    # Run the migration
    migration.create_migration(args.roles, resources_to_search, proper_names)

"""
### Scenario 1

roles_to_search = ["DistributorAdmin", "ClientAdmin"]

# Step 1: Resource and it's scope creation.
resources_to_search = {
    "SIM Automation": [
        "PATCH/v1/glass/rule/lock/{rule_uuid}/{lock_status}",
    ],"Organization": [
        "PATCH/v1/glass/rule/organization/{uuid}",
    ]
}
proper_names = {
    "PATCH/v1/glass/rule/lock/{rule_uuid}/{lock_status}": "Lock or Unlock",
    "PATCH/v1/glass/rule/organization/{uuid}": "Test"
}

python -m script.scope_permission_creation_dynamic_script \
    --roles DistributorAdmin ClientAdmin \
    --grouped_resources "SIM Automation" "PATCH/v1/glass/rule/lock/{rule_uuid}
    /{lock_status}" "Lock Rule" \
    --grouped_resources "SIM Automation" "GET/v1/glass/rule/{rule_uuid}" "Org" \
    --grouped_resources "Organization" "PATCH/v1/glass/rule/organization/{uuid}" "Test"

"""

"""
### Scenario 2

roles_to_search = ["DistributorAdmin", "ClientAdmin"]

# Step 1: Resource and it's scope creation.
resources_to_search = {
    "SIM Automation": [
        "PATCH/v1/glass/rule/lock/{rule_uuid}/{lock_status}",
        "GET/v1/glass/rule/{rule_uuid}",
    ],"Organization": [
        "PATCH/v1/glass/rule/organization/{uuid}",
    ]
}
proper_names = {
    "PATCH/v1/glass/rule/lock/{rule_uuid}/{lock_status}": "Lock or Unlock",
    "PATCH/v1/glass/rule/organization/{uuid}": "Test".
        "GET/v1/glass/rule/{rule_uuid}":"Org",
}

python -m script.scope_permission_creation_dynamic_script \
    --roles DistributorAdmin \
    --grouped_resources "SIM Automation" "PATCH/v1/glass/rule/lock/{rule_uuid}
    /{lock_status}" "Lock Rule" \
    --grouped_resources "SIM Automation" "GET/v1/glass/rule/{rule_uuid}" "Org" \
    --grouped_resources "Organization" "PATCH/v1/glass/rule/organization/{uuid}" "Test"

python -m script.scope_permission_creation_dynamic_script \
    --roles DistributorAdmin \
    --grouped_resources "Rate Plans" "PATCH/v1/glass/rate-plans/by-accounts
    /{account_id}/{id}" "Set Default Rate Plan"

"""
