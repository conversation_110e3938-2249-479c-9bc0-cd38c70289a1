import argparse
import logging

import requests

from app.common import get_access_token
from app.core.config import settings

access_token = get_access_token()


class MigrationError(BaseException):
    ...


class MigrationService:
    def __init__(self, policies: list[str], permissions: list[str]):
        self.policies = policies
        self.permissions = permissions

    def create_migration(self) -> bool:
        try:
            BASE_ADMIN_URL = f"{settings.BASE_ADMIN_URL}{settings.KEYCLOAK_REALM}"
            CLIENT_ID = settings.KEYCLOAK_CLIENT_ID

            headers = {"Authorization": f"Bearer {access_token}"}

            for policy_name in self.policies:
                # Get policy ID
                url = (
                    f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                    f"resource-server/policy?name={policy_name}"
                )
                policy_response = requests.get(
                    url, headers=headers, timeout=settings.TIMEOUT
                ).json()
                if not policy_response:
                    raise MigrationError(f"Policy not found with name: {policy_name}")
                policy_id = policy_response[0]["id"]

                for permission_name in self.permissions:
                    # Get permission
                    url = (
                        f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                        f"resource-server/permission?name={permission_name}"
                    )
                    permission_response = requests.get(
                        url, headers=headers, timeout=settings.TIMEOUT
                    )

                    if (
                        permission_response.status_code == 200
                        and len(permission_response.json()) == 1
                    ):
                        permission_data = permission_response.json()[0]
                        permission_id = permission_data["id"]

                        # Get resources
                        url = (
                            f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"  # noqa
                            f"/policy/{permission_id}/resources"
                        )
                        resource_list = requests.get(
                            url, headers=headers, timeout=settings.TIMEOUT
                        ).json()
                        resource_ids = [r["_id"] for r in resource_list]

                        # Get scopes
                        url = (
                            f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"  # noqa
                            f"/policy/{permission_id}/scopes"
                        )
                        scope_list = requests.get(
                            url, headers=headers, timeout=settings.TIMEOUT
                        ).json()
                        scope_ids = [s["id"] for s in scope_list]

                        # Get associated policies
                        url = (
                            f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/resource-server"  # noqa
                            f"/policy/{permission_id}/associatedPolicies"
                        )
                        policy_list = requests.get(
                            url, headers=headers, timeout=settings.TIMEOUT
                        ).json()
                        permission_policy_ids = [p["id"] for p in policy_list]

                        if policy_id not in permission_policy_ids:
                            permission_policy_ids.append(policy_id)

                            # Update the permission with new policy
                            url = (
                                f"{BASE_ADMIN_URL}/clients/{CLIENT_ID}/authz/"
                                f"resource-server/permission/scope/{permission_id}"
                            )
                            data = {
                                "id": permission_id,
                                "name": permission_data["name"],
                                "description": permission_data["description"],
                                "type": "scope",
                                "logic": "POSITIVE",
                                "decisionStrategy": "AFFIRMATIVE",
                                "resources": resource_ids,
                                "scopes": scope_ids,
                                "policies": permission_policy_ids,
                            }
                            response = requests.put(
                                url,
                                headers=headers,
                                json=data,
                                timeout=settings.TIMEOUT,
                            )
                            if response.status_code != 201:
                                raise MigrationError()
                            logging.info(f"{permission_name} added to {policy_name}")

            return True
        except Exception as e:
            raise MigrationError(str(e))


def parse_args():
    parser = argparse.ArgumentParser(
        description="Assign permissions to Keycloak policies."
    )
    parser.add_argument(
        "--policies",
        required=True,
        help="Comma-separated list of policies (e.g., ClientAdmin,DistributorAdmin)",
    )
    parser.add_argument(
        "--permissions",
        required=True,
        help="Comma-separated list of permissions (e.g., View Account Names,Update Multiple MSISDN Records)",  # noqa
    )
    return parser.parse_args()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    args = parse_args()

    policies = [p.strip() for p in args.policies.split(",")]
    permissions = [p.strip() for p in args.permissions.split(",")]

    migration = MigrationService(policies=policies, permissions=permissions)
    migration.create_migration()
    logging.info("✅ Successfully added permissions.")

# python -m script.07_04_2025_assign_permissions --policies="ClientAdmin,DistributorAdmin" --permissions="Update Multiple MSISDN Records" # noqa
